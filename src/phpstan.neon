includes:
    # Add this if you're using <PERSON><PERSON> and PHPStan Laravel extension
    # - vendor/nunomaduro/larastan/extension.neon

parameters:
    level: 6
    paths:
        - app

    excludePaths:
        - app/Http/Requests
        - app/Classes/Services/Criteria
        - app/Logging

    # These allow PHPStan to treat these classes as having dynamic properties/methods
    universalObjectCratesClasses:
        - Illuminate\Database\Eloquent\Model
        - Illuminate\Http\Resources\Json\JsonResource
        - Illuminate\Http\Request
        - Illuminate\Contracts\Auth\Authenticatable

    ignoreErrors:
#        - '#Called \'first\' on Laravel collection#'
        - '#Static call to instance method Carbon\\CarbonInterval::[a-zA-Z]+\(\)#'
        - '#Call to static method (info|error|warning|debug|channel|critical)\(\) on an unknown class Log#'
        - '#Call to static method [a-zA-Z]+\(\) on an unknown class [A-Za-z]+#'
        - '#no value type specified in iterable type array.#'
        - '#Call to an undefined method (Mockery\\ExpectationInterface|Mo<PERSON>y\\HigherOrderMessage)::(once|andReturnNull|with)\(\)#'
        - '#Call to an undefined method Illuminate\\Foundation\\Testing\\TestResponse::getStatusCode\(\)#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Model::getFullName\(\)#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Factories\\Factory::[a-zA-Z]+\(\)#'
        - '#Call to an undefined method Illuminate\\Contracts\\Auth\\Authenticatable::[a-zA-Z]+\(\)#'
        - '#Call to an undefined method Illuminate\\Redis\\RedisManager::subscribe\(\)#'
        - '#Call to an undefined static method Illuminate\\Support\\Facades\\Redis::[a-zA-Z]+\(\)#'
        - '#Call to an undefined method Illuminate\\Database\\DatabaseManager::[a-zA-Z]+\(\)#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Relations\\(Has|Morph)(One|Many)::(where|first|update|delete|exists)\(\)#'
        - '#Instantiated class Illuminate\\Database\\Eloquent\\Model is abstract#'

        - message: '#Call to an undefined static method \\?App\\Models\\[a-zA-Z\\\\]+::[a-zA-Z]+\(\)#'
          paths:
              - tests/*
              - database/seeds

        - message: '#Variable \$factory might not be defined#'
          path: database/factories

        - message: '#return type has no value type specified in iterable type array#'
          paths:
              - app/Transformers

        - message: '#as parameter \$collection with generic interface Illuminate\\Support\\Enumerable but does not specify its type#'
          paths:
              - app/Classes/StructuredData/Transformers

    reportUnmatchedIgnoredErrors: false
    inferPrivatePropertyTypeFromConstructor: true
