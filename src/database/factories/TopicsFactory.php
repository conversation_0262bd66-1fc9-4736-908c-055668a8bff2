<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Topic;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\TopicsTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<TopicsTrait>
 */
class TopicsFactory extends Factory {
    use TopicsTrait;

    protected $model = Topic::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
