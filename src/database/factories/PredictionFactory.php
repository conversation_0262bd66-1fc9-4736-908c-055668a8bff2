<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Prediction;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\PredictionsTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Prediction>
 */
class PredictionFactory extends Factory {
    use PredictionsTrait;

    protected $model = Prediction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
