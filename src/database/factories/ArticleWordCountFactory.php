<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ArticleWordCount;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\ArticleWordCountsTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<ArticleWordCount>
 */
class ArticleWordCountFactory extends Factory {
    use ArticleWordCountsTrait;

    protected $model = ArticleWordCount::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
