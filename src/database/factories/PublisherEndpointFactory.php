<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\PublisherEndpointsTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<PublisherEndpoint>
 */
class PublisherEndpointFactory extends Factory {
    use PublisherEndpointsTrait;

    protected $model = PublisherEndpoint::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->definePublisherEndpointData($this->faker);
    }
}
