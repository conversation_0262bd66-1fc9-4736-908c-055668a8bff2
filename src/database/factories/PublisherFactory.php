<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Publisher;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\PublisherTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Publisher>
 */
class PublisherFactory extends Factory {
    use PublisherTrait;

    protected $model = Publisher::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
