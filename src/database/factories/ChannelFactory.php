<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Channel;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\ChannelTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Channel>
 */
class ChannelFactory extends Factory {
    use ChannelTrait;

    protected $model = Channel::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
