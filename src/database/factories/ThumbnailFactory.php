<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Thumbnail;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\ThumbnailTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Thumbnail>
 */
class ThumbnailFactory extends Factory {
    use ThumbnailTrait;

    protected $model = Thumbnail::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
