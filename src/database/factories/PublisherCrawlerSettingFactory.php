<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PublisherCrawlerSetting;
use Illuminate\Database\Eloquent\Factories\Factory;
use Newswav\LaravelCentralSchema\Database\FactoryTraits\PublisherCrawlerSettingsTrait;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<PublisherCrawlerSetting>
 */
class PublisherCrawlerSettingFactory extends Factory {
    use PublisherCrawlerSettingsTrait;

    protected $model = PublisherCrawlerSetting::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array {
        return $this->defineData($this->faker);
    }
}
