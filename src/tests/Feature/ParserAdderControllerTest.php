<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Classes\ValueObjects\RawContentObject;
use App\Modules\AiModels\AiModelClient;
use App\Services\GuzzleReadService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Tests\TestCase;
use Tests\Traits\LoadsStubs;

class ParserAdderControllerTest extends TestCase {
    use LoadsStubs, DatabaseTransactions;

    public function testItParsesAndPopulatesArticleDataManually(): void {
        $publisher = $this->createPublisher(['id' => 1]);
        $channel   = $this->createChannel(['publisher_id' => $publisher->id]);
        $htmlLink  = $this->getHtmlArticleLinkStub();

        $this->createPublisherCrawlerSetting([
            'publisher_id'         => $publisher->id,
            'channel_id'           => $channel->id,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
        ]);

        $guzzleReadServiceMock = Mockery::mock(GuzzleReadService::class);
        $guzzleReadServiceMock->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with($htmlLink)
            ->andReturn(new RawContentObject($this->getFullHtmlFetchedStub()));
        $this->app->instance(GuzzleReadService::class, $guzzleReadServiceMock);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn($this->getParsedDataStub());
        $this->app->instance(AiModelClient::class, $aiMockClientMock);

        $response  = $this->postJson('/api/parse-manually', [
            'publisher_id'           => $publisher->id,
            'channel_id'             => $channel->id,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $response->assertStatus(200);
        $this->assertJson($response->getContent());
    }
}
