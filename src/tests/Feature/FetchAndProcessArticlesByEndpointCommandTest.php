<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\FetcherCommandException;
use App\Jobs\ProcessArticleDataJob;
use App\Modules\AiModels\AiModelClient;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Mockery;
use Tests\TestCase;

class FetchAndProcessArticlesByEndpointCommandTest extends TestCase {
    public function testItExecutesFetchAndProcessLogicWithHeadlessBrowserFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => true,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => null,
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/rss',
            'last_checked' => null,
        ]);

        $mockRawContent   = '<rss><channel><item><title>Test Article</title><link>https://example.com/article/1</link></item></channel></rss>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $headlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $headlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/rss', 'Test Agent')
            ->andReturn($rawContentObject);
        $this->app->instance(HeadlessBrowserService::class, $headlessBrowserService);

        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => (string) $publisherEndpoint->id,
        ])->assertSuccessful();

        $fakeQueue->assertPushed(ProcessArticleDataJob::class);
    }

    public function testItExecutesFetchAndProcessLogicWithBypassCloudflareFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => true,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => 'Custom prompt',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'       => 'https://example.com/news',
            'use_ai_parsing' => true,
            'last_checked'   => null,
        ]);

        $mockRawContent   = '<html><body><h1>News Page</h1><a href="https://example.com/article/1">Article 1</a></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('["https://example.com/article/1"]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/news', 'Test Agent', null)
            ->andReturn($rawContentObject);
        $this->app->instance(BypassCloudflareService::class, $bypassCloudflareService);

        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => (string) $publisherEndpoint->id,
        ])->assertSuccessful();
        $fakeQueue->assertPushed(ProcessArticleDataJob::class);

    }

    public function testItExecutesFetchAndProcessLogicWithGuzzleReadServiceFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => 'Custom prompt',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'       => 'https://example.com/news',
            'use_ai_parsing' => true,
            'last_checked'   => null,
        ]);

        $mockRawContent   = '<html><body><h1>News Page</h1><a href="https://example.com/article/1">Article 1</a></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('["https://example.com/article/1"]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/news', 'Test Agent')
            ->andReturn($rawContentObject);
        $this->app->instance(GuzzleReadService::class, $guzzleReadService);

        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => (string) $publisherEndpoint->id,
        ])->assertSuccessful();
        $fakeQueue->assertPushed(ProcessArticleDataJob::class);
    }

    public function testItHandlesNoArticleDataFoundScenario(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/empty',
            'last_checked' => null,
        ]);

        $mockRawContent   = '<html><body><h1>Empty Page</h1></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('[]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->andReturn($rawContentObject);
        $this->app->instance(GuzzleReadService::class, $guzzleReadService);

        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => (string) $publisherEndpoint->id,
        ])->assertSuccessful();
        $fakeQueue->assertNothingPushed();
    }

    public function testItFailsWhenEndpointDoesNotMeetCriteria(): void {
        $this->expectException(FetcherCommandException::class);
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'           => false,
            'use_smart_crawler' => true,
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/test',
            'last_checked' => null,
        ]);

        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => (string) $publisherEndpoint->id,
        ]);
    }

    public function testItFailsWhenEndpointDoesNotExist(): void {
        $this->expectException(FetcherCommandException::class);
        $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => '-999999',
        ]);
    }
}
