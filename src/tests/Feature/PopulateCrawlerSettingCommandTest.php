<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\PublisherCrawlerSetting;
use Tests\TestCase;

class PopulateCrawlerSettingCommandTest extends TestCase {
    public function testItPopulateCrawlerSetting(): void {
        $publisherId        = 1234;
        $channelId          = 5678;
        $host               = 'https://example.com';
        $worker             = 'w1';
        $frequency          = 15;
        $enabled            = true;
        $needRandom         = true;
        $useSmartCrawler    = true;
        $toPuppeteer        = false;
        $customUserAgent    = 'TestAgent';
        $customPrompt       = 'Test prompt';
        $partner            = 1;
        $type               = 'rss';
        $customFetcher      = 'custom-fetcher';
        $useHeadlessBrowser = false;
        $endpointUrl        = 'https://example.com/feed';
        $defaultCategories  = 'news,tech';
        $defaultTopics      = 'ai,cloud';
        $hasProxyImage      = true;
        $useAiParsing       = true;

        $this->artisan('populate-crawler-setting')
            ->expectsQuestion('Publisher ID', (string) $publisherId)
            ->expectsQuestion('Channel ID', (string) $channelId)
            ->expectsQuestion('Host (e.g., https://example.com)', $host)
            ->expectsQuestion('Worker (w1, w2, w3, w4, w5, w6)', $worker)
            ->expectsQuestion('Frequency (minutes)', (string) $frequency)
            ->expectsConfirmation('Enabled?', 'yes')
            ->expectsConfirmation('Need random? (optional)', 'yes')
            ->expectsConfirmation('Use smart crawler?', 'yes')
            ->expectsConfirmation('Use Puppeteer?', 'no')
            ->expectsQuestion('Custom user agent (optional)', $customUserAgent)
            ->expectsConfirmation('Do you want to add a custom prompt?', 'yes')
            ->expectsQuestion('', $customPrompt)
            ->expectsQuestion('', 'END')
            ->expectsQuestion('Partner (1/0)', (string) $partner)
            ->expectsQuestion('Type (optional)', $type)
            ->expectsQuestion('Custom fetcher (optional)', $customFetcher)
            ->expectsConfirmation('Use headless browser?', 'no')
            ->expectsQuestion('Endpoint URL', $endpointUrl)
            ->expectsQuestion('Default categories (comma-separated, optional)', $defaultCategories)
            ->expectsQuestion('Default topics (comma-separated, optional)', $defaultTopics)
            ->expectsConfirmation('Use proxy image?', 'yes')
            ->expectsConfirmation('Use AI parsing? (AI parsing is NOT recommended due to token limit and cost consuption)', 'yes')
            ->assertExitCode(0);

        $this->assertDatabaseHas('publisher_crawler_settings', [
            'publisher_id'         => $publisherId,
            'channel_id'           => $channelId,
            'host'                 => $host,
            'worker'               => $worker,
            'frequency'            => $frequency,
            'enabled'              => (int) $enabled,
            'need_random'          => (int) $needRandom,
            'use_smart_crawler'    => (int) $useSmartCrawler,
            'to_puppeteer'         => (int) $toPuppeteer,
            'custom_user_agent'    => $customUserAgent,
            'custom_prompt'        => $customPrompt,
            'partner'              => $partner,
            'type'                 => $type,
            'crawl_type'           => 'article',
            'custom_fetcher'       => $customFetcher,
            'use_headless_browser' => (int) $useHeadlessBrowser,
        ]);

        $crawlerSetting = PublisherCrawlerSetting::where('publisher_id', $publisherId)
            ->where('channel_id', $channelId)
            ->first();

        $this->assertNotNull($crawlerSetting);

        $this->assertDatabaseHas('publisher_endpoints', [
            'crawler_setting_id' => $crawlerSetting->id,
            'endpoint'           => $endpointUrl,
            'default_categories' => $defaultCategories,
            'default_topics'     => $defaultTopics,
            'has_proxy_image'    => (int) $hasProxyImage,
            'use_ai_parsing'     => (int) $useAiParsing,
        ]);
    }

    public function testItUpdateCrawlerSettingCommand(): void {
        $publisherId        = 1234;
        $channelId          = 5678;
        $host               = 'https://example.com';
        $worker             = 'w1';
        $frequency          = 15;
        $enabled            = true;
        $needRandom         = true;
        $useSmartCrawler    = true;
        $toPuppeteer        = false;
        $customUserAgent    = 'TestAgent';
        $customPrompt       = 'Test prompt';
        $partner            = 1;
        $type               = 'rss';
        $customFetcher      = 'custom-fetcher';
        $useHeadlessBrowser = false;
        $endpointUrl        = 'https://example.com/feed';
        $defaultCategories  = 'news,tech';
        $defaultTopics      = 'ai,cloud';
        $hasProxyImage      = true;
        $useAiParsing       = true;

        $crawlerSetting = PublisherCrawlerSetting::create([
            'publisher_id'         => $publisherId,
            'channel_id'           => $channelId,
            'host'                 => $host,
            'worker'               => $worker,
            'frequency'            => $frequency,
            'enabled'              => (int) $enabled,
            'need_random'          => (int) $needRandom,
            'use_smart_crawler'    => (int) $useSmartCrawler,
            'to_puppeteer'         => (int) $toPuppeteer,
            'custom_user_agent'    => $customUserAgent,
            'custom_prompt'        => $customPrompt,
            'partner'              => $partner,
            'type'                 => $type,
            'crawl_type'           => 'article',
            'custom_fetcher'       => $customFetcher,
            'use_headless_browser' => (int) $useHeadlessBrowser,
        ]);

        $this->artisan('populate-crawler-setting')
            ->expectsQuestion('Publisher ID', (string) $publisherId)
            ->expectsQuestion('Channel ID', (string) $channelId)
            ->expectsQuestion('Host (e.g., https://example.com)', $host)
            ->expectsQuestion('Worker (w1, w2, w3, w4, w5, w6)', $worker)
            ->expectsQuestion('Frequency (minutes)', (string) $frequency)
            ->expectsConfirmation('Enabled?', 'yes')
            ->expectsConfirmation('Need random? (optional)', 'yes')
            ->expectsConfirmation('Use smart crawler?', 'yes')
            ->expectsConfirmation('Use Puppeteer?', 'no')
            ->expectsQuestion('Custom user agent (optional)', $customUserAgent)
            ->expectsConfirmation('Do you want to add a custom prompt?', 'yes')
            ->expectsQuestion('', $customPrompt)
            ->expectsQuestion('', 'END')
            ->expectsQuestion('Partner (1/0)', (string) $partner)
            ->expectsQuestion('Type (optional)', $type)
            ->expectsQuestion('Custom fetcher (optional)', $customFetcher)
            ->expectsConfirmation('Use headless browser?', 'no')
            ->expectsConfirmation('Update/Create endpoint?', 'yes')
            ->expectsQuestion('Endpoint URL', $endpointUrl)
            ->expectsQuestion('Default categories (comma-separated, optional)', $defaultCategories)
            ->expectsQuestion('Default topics (comma-separated, optional)', $defaultTopics)
            ->expectsConfirmation('Use proxy image?', 'yes')
            ->expectsConfirmation('Use AI parsing? (AI parsing is NOT recommended due to token limit and cost consuption)', 'yes')
            ->assertExitCode(0);

        $this->assertDatabaseHas('publisher_crawler_settings', [
            'publisher_id'         => $publisherId,
            'channel_id'           => $channelId,
            'host'                 => $host,
            'worker'               => $worker,
            'frequency'            => $frequency,
            'enabled'              => (int) $enabled,
            'need_random'          => (int) $needRandom,
            'use_smart_crawler'    => (int) $useSmartCrawler,
            'to_puppeteer'         => (int) $toPuppeteer,
            'custom_user_agent'    => $customUserAgent,
            'custom_prompt'        => $customPrompt,
            'partner'              => $partner,
            'type'                 => $type,
            'crawl_type'           => 'article',
            'custom_fetcher'       => $customFetcher,
            'use_headless_browser' => (int) $useHeadlessBrowser,
        ]);

        $this->assertDatabaseHas('publisher_endpoints', [
            'crawler_setting_id' => $crawlerSetting->id,
            'endpoint'           => $endpointUrl,
            'default_categories' => $defaultCategories,
            'default_topics'     => $defaultTopics,
            'has_proxy_image'    => (int) $hasProxyImage,
            'use_ai_parsing'     => (int) $useAiParsing,
        ]);
    }
}
