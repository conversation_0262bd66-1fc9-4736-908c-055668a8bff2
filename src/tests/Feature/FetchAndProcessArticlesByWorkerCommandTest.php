<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Classes\ValueObjects\RawContentObject;
use App\Jobs\ProcessArticleDataJob;
use App\Modules\AiModels\AiModelClient;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Mockery;
use Tests\TestCase;

class FetchAndProcessArticlesByWorkerCommandTest extends TestCase {
    public function testItExecutesFetchAndProcessLogicWithHeadlessBrowserFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => true,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => null,
            'worker'               => 'w1',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/rss',
            'last_checked' => null,
        ]);

        $mockRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->once()
            ->with('w1')
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$publisherEndpoint]));
        $mockRepository->shouldReceive('updateLastChecked')
            ->once()
            ->with($publisherEndpoint->id)
            ->andReturn(true);
        $this->app->instance(PublisherEndpointRepository::class, $mockRepository);

        $mockRawContent   = '<rss><channel><item><title>Test Article</title><link>https://example.com/article/1</link></item></channel></rss>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $headlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $headlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/rss', 'Test Agent')
            ->andReturn($rawContentObject);
        $this->app->instance(HeadlessBrowserService::class, $headlessBrowserService);

        $this->artisan('fetch-and-process-articles-by-worker', [
            'workerId' => (string) $publisherCrawlerSetting->worker,
        ])->assertSuccessful();
        $fakeQueue->assertPushed(ProcessArticleDataJob::class);
    }

    public function testItExecutesFetchAndProcessLogicWithBypassCloudflareFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => true,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => 'Custom prompt',
            'worker'               => 'w1',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/news',
            'last_checked' => null,
        ]);

        // Mock the repository to only return our test data
        $mockRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->once()
            ->with('w1')
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$publisherEndpoint]));
        $mockRepository->shouldReceive('updateLastChecked')
            ->once()
            ->with($publisherEndpoint->id)
            ->andReturn(true);
        $this->app->instance(PublisherEndpointRepository::class, $mockRepository);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('["https://example.com/article/1"]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);
        $mockRawContent   = '<html><body><h1>News Page</h1><a href="https://example.com/article/1">Article 1</a></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/news', 'Test Agent', null)
            ->andReturn($rawContentObject);
        $this->app->instance(BypassCloudflareService::class, $bypassCloudflareService);

        $this->artisan('fetch-and-process-articles-by-worker', [
            'workerId' => (string) $publisherCrawlerSetting->worker,
        ])->assertSuccessful();
        $fakeQueue->assertPushed(ProcessArticleDataJob::class);
    }

    public function testItExecutesFetchAndProcessLogicWithGuzzleReadServiceFlow(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'custom_user_agent'    => 'Test Agent',
            'custom_prompt'        => 'Custom prompt',
            'worker'               => 'w1',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/news',
            'last_checked' => null,
        ]);

        $mockRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->once()
            ->with('w1')
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$publisherEndpoint]));
        $mockRepository->shouldReceive('updateLastChecked')
            ->once()
            ->with($publisherEndpoint->id)
            ->andReturn(true);
        $this->app->instance(PublisherEndpointRepository::class, $mockRepository);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('["https://example.com/article/1"]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);
        $mockRawContent   = '<html><body><h1>News Page</h1><a href="https://example.com/article/1">Article 1</a></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with('https://example.com/news', 'Test Agent')
            ->andReturn($rawContentObject);
        $this->app->instance(GuzzleReadService::class, $guzzleReadService);

        $this->artisan('fetch-and-process-articles-by-worker', [
            'workerId' => (string) $publisherCrawlerSetting->worker,
        ])->assertSuccessful();
        $fakeQueue->assertPushed(ProcessArticleDataJob::class);
    }

    public function testItHandlesNoArticleDataFoundScenario(): void {
        $fakeQueue               = $this->getFakeQueue();
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting([
            'enabled'              => true,
            'use_smart_crawler'    => true,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'worker'               => 'w1',
        ]);

        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();
        $publisherEndpoint->update([
            'endpoint'     => 'https://example.com/empty',
            'last_checked' => null,
        ]);

        // Mock the repository to only return our test data
        $mockRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->once()
            ->with('w1')
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$publisherEndpoint]));
        $mockRepository->shouldReceive('updateLastChecked')
            ->once()
            ->with($publisherEndpoint->id)
            ->andReturn(true);
        $this->app->instance(PublisherEndpointRepository::class, $mockRepository);

        $aiMockClientMock = Mockery::mock(AiModelClient::class);
        $aiMockClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('[]');
        $this->app->instance(AiModelClient::class, $aiMockClientMock);
        $mockRawContent   = '<html><body><h1>Empty Page</h1></body></html>';
        $rawContentObject = new RawContentObject($mockRawContent);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->andReturn($rawContentObject);
        $this->app->instance(GuzzleReadService::class, $guzzleReadService);

        $this->artisan('fetch-and-process-articles-by-worker', [
            'workerId' => (string) $publisherCrawlerSetting->worker,
        ])->assertSuccessful();
        $fakeQueue->assertNothingPushed();
    }
}
