<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\PublisherCrawlerSetting;
use Illuminate\Database\Eloquent\Collection;

trait TestPublisherCrawlerSettings {
    public function createPublisherCrawlerSetting(array $data = []): PublisherCrawlerSetting {
        return PublisherCrawlerSetting::factory()->create($data);
    }

    public function createPublisherCrawlerSettings(int $count = 1, array $data = []): Collection {
        return PublisherCrawlerSetting::factory()->count($count)->create($data);
    }

    public function updatePublisherCrawlerSetting(PublisherCrawlerSetting $publisherCrawlerSetting, array $data = []): PublisherCrawlerSetting {
        $publisherCrawlerSetting->update($data);

        return $publisherCrawlerSetting;
    }
}
