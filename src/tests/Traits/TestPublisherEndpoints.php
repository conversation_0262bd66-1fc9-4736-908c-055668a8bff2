<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Collection;

trait TestPublisherEndpoints {
    public function createPublisherEndpoints(int $count, int $crawlerSettingId): Collection {
        return PublisherEndpoint::factory()->count($count)->create([
            'crawler_setting_id' => $crawlerSettingId,
        ]);
    }

    public function updatePublisherEndpoint(PublisherEndpoint $publisherEndpoint, array $data = []): PublisherEndpoint {
        $publisherEndpoint->update($data);

        return $publisherEndpoint;
    }
}
