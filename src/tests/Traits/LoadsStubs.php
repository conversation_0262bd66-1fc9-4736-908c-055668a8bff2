<?php

declare(strict_types=1);

namespace Tests\Traits;

trait LoadsStubs {
    protected function getStub(string $name): array | string {
        $content = file_get_contents(__DIR__ . '/../Stubs/' . $name);

        if (str_ends_with($name, '.json')) {
            return json_decode($content, true);
        }

        return $content;
    }

    protected function getRssItemStubWithoutGuid(): string {
        return $this->getStub('rss_item_without_guid.txt');
    }

    protected function getRssItemStubWithGuid(): string {
        return $this->getStub('rss_item_with_guid.txt');
    }

    protected function getHtmlArticleLinkStub(): string {
        return $this->getStub('html_link.txt');
    }

    protected function getParsedDataStub(): string {
        return $this->getStub('parsed_data_example.txt');
    }

    protected function getFullHtmlFetchedStub(): string {
        return $this->getStub('full_html_fetched.txt');
    }
}
