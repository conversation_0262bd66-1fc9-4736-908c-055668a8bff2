<?php

declare(strict_types=1);

namespace Tests\Traits;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Testing\Fakes\EventFake;
use Illuminate\Support\Testing\Fakes\QueueFake;

trait FakeQueuesEvents {
    public function getFakeQueue(): QueueFake {
        Queue::fake();

        return resolve('queue');
    }

    public function getFakeEvents(): EventFake {
        Event::fake();

        return resolve('events');
    }
}
