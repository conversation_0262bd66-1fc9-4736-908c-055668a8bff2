<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Classes\Constants\ServerParameters;
use Illuminate\Http\Request;

trait TestRequestGenerators {
    protected function createRequest(array $parameters = [], array $files = [], string $method = ServerParameters::HTTP_METHOD_GET): Request {
        return $this->createRequestFromParameters($method, $parameters, $files);
    }

    private function createRequestFromParameters(string $method, array $parameters, array $files): Request {
        $request = new Request();

        if ($method === ServerParameters::HTTP_METHOD_GET) {
            $request->initialize($parameters);
        }

        if ($method !== ServerParameters::HTTP_METHOD_GET) {
            $request->initialize([], $parameters, [], [], $files);
        }

        $request->setMethod($method);

        return $request;
    }
}
