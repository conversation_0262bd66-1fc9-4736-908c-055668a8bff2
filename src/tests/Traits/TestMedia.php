<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Media;
use Illuminate\Database\Eloquent\Collection;

trait TestMedia {
    public function createMedia(array $data = []): Media {
        return Media::factory()->create($data);
    }

    public function createMedias(int $count, array $data = []): Collection {
        return Media::factory()->count($count)->create($data);
    }
}
