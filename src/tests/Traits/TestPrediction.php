<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Prediction;

trait TestPrediction {
    public function createPrediction(array $data = []): Prediction {
        return Prediction::factory()->create($data);
    }

    public function updatePrediction(Prediction $prediction, array $data = []): Prediction {
        $prediction->update($data);

        return $prediction;
    }
}
