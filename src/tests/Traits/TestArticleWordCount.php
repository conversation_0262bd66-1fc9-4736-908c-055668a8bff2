<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\ArticleWordCount;

trait TestArticleWordCount {
    public function createArticleWordCount(array $data = []): ArticleWordCount {
        return ArticleWordCount::factory()->create($data);
    }

    public function updateArticleWordCount(ArticleWordCount $articleWordCount, array $data = []): ArticleWordCount {
        $articleWordCount->update($data);

        return $articleWordCount;
    }
}
