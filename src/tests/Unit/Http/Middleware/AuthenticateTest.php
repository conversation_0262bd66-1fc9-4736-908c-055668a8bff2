<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Middleware;

use App\Exceptions\UnauthorisedException;
use App\Http\Middleware\Authenticate;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class AuthenticateTest extends TestCase {
    public function testPassesThroughInNonProductionEnvironment(): void {
        $this->app->instance('env', 'local');

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request  = Request::create('/test', 'GET');
        $response = $middleware->handle($request, $next);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(['message' => 'success'], $response->getData(true));
    }

    public function testThrowsUnauthorisedExceptionWhenAuthorizationHeaderMissing(): void {
        $this->app->instance('env', 'production');

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request = Request::create('/test', 'GET');

        $this->expectException(UnauthorisedException::class);
        $middleware->handle($request, $next);
    }

    public function testThrowsUnauthorisedExceptionWhenJwtIsInvalid(): void {
        $this->app->instance('env', 'production');

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer invalid.jwt.token');

        Http::fake([
            'https://www.googleapis.com/oauth2/v3/certs' => Http::response([
                'keys' => [
                    [
                        'kty' => 'RSA',
                        'use' => 'sig',
                        'kid' => 'test-kid',
                        'n'   => 'test-n',
                        'e'   => 'AQAB',
                    ],
                ],
            ]),
        ]);

        $this->expectException(UnauthorisedException::class);
        $middleware->handle($request, $next);
    }

    public function testThrowsUnauthorisedExceptionWhenHttpThrowsException(): void {
        $this->app->instance('env', 'production');

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer test.jwt.token');

        Http::fake(function (): void {
            throw new Exception('Network error');
        });

        $this->expectException(UnauthorisedException::class);
        $middleware->handle($request, $next);
    }

    public function testItPassesThroughWhenJwtMatchesSecret(): void {
        $this->app->instance('env', 'production');

        config(['auth.jwt.secret' => 'test-secret']);

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer test-secret');

        $response = $middleware->handle($request, $next);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(['message' => 'success'], $response->getData(true));
    }

    public function testItDoesNotPassThroughWhenJwtDoesNotMatchSecret(): void {
        $this->app->instance('env', 'production');

        config(['auth.jwt.secret' => 'test-secret']);

        $middleware = new Authenticate();
        $next       = function ($request) {
            return new JsonResponse(['message' => 'success']);
        };

        $request = Request::create('/test', 'GET');
        $request->headers->set('Authorization', 'Bearer invalid-secret');

        $this->expectException(UnauthorisedException::class);
        $middleware->handle($request, $next);
    }
}
