<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Services;

use App\Jobs\ProcessArticleDataJob;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use Tests\TestCase;

class PassesArticleDataToParserAdderServiceTest extends TestCase {
    public function testItPassArticleDataToParserAdderService(): void {
        $fakeQueue             = $this->getFakeQueue();
        $article               = $this->generator->paragraph();
        $articleData           = [$article];
        $logic                 = new PassesArticleDataToParserAdder();
        $logic->execute(1, 2, $articleData, true, null, false, false, true);

        $fakeQueue->assertPushed(ProcessArticleDataJob::class, function ($job) use ($article) {
            return $job->articleProcessingData->getPublisherId() === 1
                && $job->articleProcessingData->getChannelId() === 2
                && $job->articleProcessingData->getArticleDataRssItem() === $article
                && $job->articleProcessingData->getArticleDataHtmlLink() === null
                && $job->articleProcessingData->getCustomPrompt() === ''
                && $job->articleProcessingData->useHeadlessBrowser() === false;
        });

    }

    public function testItExecuteForNonLocal(): void {
        $fakeQueue    = $this->getFakeQueue();
        $article      = $this->generator->paragraph();
        $articleData  = [$article];

        $logic = new PassesArticleDataToParserAdder();
        $logic->execute(1, 2, $articleData, true, null, false, false, true);

        $fakeQueue->assertPushed(ProcessArticleDataJob::class, function ($job) use ($article) {
            return $job->articleProcessingData->getPublisherId() === 1
                && $job->articleProcessingData->getChannelId() === 2
                && $job->articleProcessingData->getArticleDataRssItem() === $article
                && $job->articleProcessingData->getArticleDataHtmlLink() === null
                && $job->articleProcessingData->getCustomPrompt() === ''
                && $job->articleProcessingData->useHeadlessBrowser() === false;
        });
    }
}
