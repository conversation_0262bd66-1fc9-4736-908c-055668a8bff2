<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\ExtractsRssItemsFromRawContent;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;

class ExtractsRssItemsFromRawContentTest extends TestCase {
    public function testItExtractRssItemsFromRawContent(): void {
        $xml = <<<XML
<rss>
  <channel>
    <item>
      <title>Test</title>
      <link>http://test.com</link>
    </item>
  </channel>
</rss>
XML;

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com')
            ->andReturn('http://test.com');

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findByUrlAndContentMd5')->once()->andReturn(null);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(1, $result);
        static::assertStringContainsString('<item>', $result[0]);
    }

    public function testItThrowsServiceExceptionIfInvalidXml(): void {
        $invalidXml = '<rss><invalid></rss>';

        $contentHelperMock     = Mockery::mock(ContentHelper::class);
        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);

        $this->expectException(ServiceException::class);
        $this->expectExceptionMessage('Failed to load XML');
        $this->expectExceptionCode(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR);

        $service->execute($invalidXml);
    }

    public function testItFiltersOutDuplicateArticles(): void {
        $xml = <<<XML
<rss>
  <channel>
    <item>
      <title>Duplicate</title>
      <link>http://test.com/duplicate</link>
    </item>
  </channel>
</rss>
XML;

        $mockArticle = $this->createArticle([
            'canonicalURL' => 'http://test.com/duplicate',
            'content_md5'  => md5($xml),
        ]);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com/duplicate')
            ->andReturn('http://test.com/duplicate');

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findByUrlAndContentMd5')->once()->andReturn($mockArticle);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(0, $result);
    }

    public function testItExtractsUsingOrigLinkWhenLinkMissing(): void {
        $xml = <<<XML
<rss>
  <channel>
    <item>
      <title>Test</title>
      <origLink>http://test.com/orig</origLink>
    </item>
  </channel>
</rss>
XML;

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com/orig')
            ->andReturn('http://test.com/orig');

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findByUrlAndContentMd5')->once()->andReturn(null);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(1, $result);
    }

    public function testItExtractsUsingGuidWhenLinkAndOrigLinkMissing(): void {
        $xml = <<<XML
<rss>
  <channel>
    <item>
      <title>Test</title>
      <guid>http://test.com/guid</guid>
    </item>
  </channel>
</rss>
XML;

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com/guid')
            ->andReturn('http://test.com/guid');

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findByUrlAndContentMd5')->once()->andReturn(null);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(1, $result);
    }

    public function testItExtractsUsingHrefAttributeInLink(): void {
        $xml = <<<XML
<rss>
  <channel>
    <item>
      <title>Test</title>
      <link href="http://test.com/href" />
    </item>
  </channel>
</rss>
XML;

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com/href')
            ->andReturn('http://test.com/href');

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findByUrlAndContentMd5')->once()->andReturn(null);

        $service = new ExtractsRssItemsFromRawContent($articleRepositoryMock, $contentHelperMock);
        $result  = $service->execute($xml);
        static::assertCount(1, $result);
    }
}
