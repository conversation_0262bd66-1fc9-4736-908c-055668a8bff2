<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Logics\FetchAndProcessEndpointForWorkerLogic;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Mockery;
use Tests\TestCase;

class FetchAndProcessEndpointForWorkerLogicTest extends TestCase {
    public function testItFetchAndProcessEndpointWithWorkerIdUsingHeadlessBrowser(): void {
        $workerId       = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => true,
            'worker'               => $workerId,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$mockEndpointWithCrawlerSettings]));

        $mockPublisherEndpointRepository->shouldReceive('updateLastChecked')
            ->once()
            ->andReturn(true);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockHeadlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $mockHeadlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with(
                $mockEndpointWithCrawlerSettings->endpoint,
                $mockEndpointWithCrawlerSettings->crawlerSetting->custom_user_agent
            )
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $mockBypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $mockBypassCloudflareService->shouldReceive('getRawHtmlOrRss')->never();

        $mockGuzzleReadService = Mockery::mock(GuzzleReadService::class);
        $mockGuzzleReadService->shouldReceive('getRawHtmlOrRss')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            $mockBypassCloudflareService,
            $mockHeadlessBrowserService,
            $mockGuzzleReadService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithWorkerIdUsingBypassCloudflare(): void {
        $workerId       = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => false,
            'to_puppeteer'         => true,
            'worker'               => $workerId,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$mockEndpointWithCrawlerSettings]));

        $mockPublisherEndpointRepository->shouldReceive('updateLastChecked')
            ->once()
            ->andReturn(true);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockBypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $mockBypassCloudflareService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with(
                $mockEndpointWithCrawlerSettings->endpoint,
                $mockEndpointWithCrawlerSettings->crawlerSetting->custom_user_agent,
                $mockEndpointWithCrawlerSettings->crawlerSetting->custom_cookies,
            )
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $mockGuzzleReadService = Mockery::mock(GuzzleReadService::class);
        $mockGuzzleReadService->shouldReceive('getRawHtmlOrRss')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            $mockBypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            $mockGuzzleReadService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithWorkerIdUsingGuzzleReadService(): void {
        $workerId       = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'worker'               => $workerId,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$mockEndpointWithCrawlerSettings]));

        $mockPublisherEndpointRepository->shouldReceive('updateLastChecked')
            ->once()
            ->andReturn(true);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->once()
            ->andReturn(false);

        $mockGuzzleReadService = Mockery::mock(GuzzleReadService::class);
        $mockGuzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->with(
                $mockEndpointWithCrawlerSettings->endpoint,
                $mockEndpointWithCrawlerSettings->crawlerSetting->custom_user_agent
            )
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->once()
            ->andReturn(true);

        $mockBypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $mockBypassCloudflareService->shouldReceive('getRawHtmlOrRss')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            $mockBypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            $mockGuzzleReadService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithInvalidWorkerId(): void {
        $workerId = 'invalid';

        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([]));

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')->never();

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            $mockPassesArticleDataToParserAdderService,
            Mockery::mock(ContentHelper::class),
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithNoArticleData(): void {
        $workerId = 'w1';

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'worker'               => $workerId,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$mockEndpointWithCrawlerSettings]));

        $mockPublisherEndpointRepository->shouldReceive('updateLastChecked')
            ->once()
            ->andReturn(true);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockGuzzleReadService = Mockery::mock(GuzzleReadService::class);
        $mockGuzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->once()
            ->andReturn([]);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $mockGuzzleReadService,
            $mockPassesArticleDataToParserAdderService,
            Mockery::mock(ContentHelper::class),
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithWorkerIdWithTimeout(): void {
        $workerId = 'w1';

        $mockCrawlerSettings = $this->createPublisherCrawlerSettings(1, [
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
            'worker'               => $workerId,
        ])->first();

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(1, $mockCrawlerSettings->id)->first();
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(new \Illuminate\Database\Eloquent\Collection([$mockEndpointWithCrawlerSettings]));

        $mockPublisherEndpointRepository->shouldReceive('updateLastChecked')
            ->once()
            ->andReturn(true);

        $mockGuzzleReadService = Mockery::mock(GuzzleReadService::class);
        $mockGuzzleReadService->shouldReceive('getRawHtmlOrRss')
            ->once()
            ->andReturn(null);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')->never();

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')->never();

        $service = new FetchAndProcessEndpointForWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $mockGuzzleReadService,
            $mockPassesArticleDataToParserAdderService,
            Mockery::mock(ContentHelper::class),
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }
}
