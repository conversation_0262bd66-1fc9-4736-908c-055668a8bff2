<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleObject;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;

class CreatesArticleTest extends TestCase {
    public function testItCreateArticle(): void {
        $articleObject   = Mockery::mock(ArticleObject::class);
        $expectedArticle = $this->createArticle();

        $articleRepository = Mockery::mock(ArticleRepository::class);
        $articleRepository
            ->shouldReceive('createArticleFromObject')
            ->once()
            ->with($articleObject)
            ->andReturn($expectedArticle);

        $service = new CreatesArticle($articleRepository);
        $result  = $service->execute($articleObject);

        $this->assertSame($expectedArticle, $result);
    }
}
