<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\ExtractsRssItemsFromRawContent;
use App\Modules\ParserAdder\Services\ParsesArticleWithDefaultParser;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;
use Tests\Traits\LoadsStubs;

class ParsesArticleWithDefaultParserTest extends TestCase {
    use LoadsStubs;

    public function testItParsesArticleWithDefaultParserWithNoGuid(): void {
        $articleRepository = Mockery::mock(ArticleRepository::class);
        $contentHelper     = Mockery::mock(ContentHelper::class);
        $contentHelper->shouldReceive('getCanonicalUrl')
            ->andReturn('https://example.com/article');
        $articleRepository->shouldReceive('findByUrlAndContentMd5')
            ->andReturn(null);
        $extractor      = new ExtractsRssItemsFromRawContent($articleRepository, $contentHelper);
        $extractedItems = $extractor->execute($this->getRssItemStubWithoutGuid());

        $service = new ParsesArticleWithDefaultParser();
        $result  = $service->execute($extractedItems[0]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('full_content', $result);
        $this->assertEquals('Test Article', $result['title']);
    }

    public function testItParsesArticleWithDefaultParserWithGuid(): void {
        $articleRepository = Mockery::mock(ArticleRepository::class);
        $contentHelper     = Mockery::mock(ContentHelper::class);
        $contentHelper->shouldReceive('getCanonicalUrl')
            ->andReturn('https://example.com/article');
        $articleRepository->shouldReceive('findByUrlAndContentMd5')
            ->andReturn(null);
        $extractor      = new ExtractsRssItemsFromRawContent($articleRepository, $contentHelper);
        $extractedItems = $extractor->execute($this->getRssItemStubWithGuid());

        $service = new ParsesArticleWithDefaultParser();
        $result  = $service->execute($extractedItems[0]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('full_content', $result);
        $this->assertEquals('Test Article', $result['title']);
        $this->assertEquals(123, $result['article_id']);
    }

    public function testItParsesArticleWithDefaultParserWithEnclosure(): void {
        $rawContent        = '<item><title>Test Article</title><link>https://example.com/article</link><description>Test Description</description><content:encoded>Test Content</content:encoded><enclosure url="https://example.com/image.jpg" length="12345" type="image/jpeg" /></item>';
        $articleRepository = Mockery::mock(ArticleRepository::class);
        $contentHelper     = Mockery::mock(ContentHelper::class);
        $contentHelper->shouldReceive('getCanonicalUrl')
            ->andReturn('https://example.com/article');
        $articleRepository->shouldReceive('findByUrlAndContentMd5')
            ->andReturn(null);
        $extractor      = new ExtractsRssItemsFromRawContent($articleRepository, $contentHelper);
        $extractedItems = $extractor->execute($rawContent);

        $service = new ParsesArticleWithDefaultParser();
        $result  = $service->execute($extractedItems[0]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('full_content', $result);
        $this->assertEquals('Test Article', $result['title']);
        $this->assertEquals('https://example.com/image.jpg', $result['cover_image_url']['url']);
    }

    public function testItParsesArticleWithoutFullContent(): void {
        $rawContent        = '<item><title>Test Article</title><link>https://example.com/article</link><description>Test Description</description></item>';
        $articleRepository = Mockery::mock(ArticleRepository::class);
        $contentHelper     = Mockery::mock(ContentHelper::class);
        $contentHelper->shouldReceive('getCanonicalUrl')
            ->andReturn('https://example.com/article');
        $articleRepository->shouldReceive('findByUrlAndContentMd5')
            ->andReturn(null);
        $extractor      = new ExtractsRssItemsFromRawContent($articleRepository, $contentHelper);
        $extractedItems = $extractor->execute($rawContent);

        $service = new ParsesArticleWithDefaultParser();
        $result  = $service->execute($extractedItems[0]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('full_content', $result);
        $this->assertEquals('Test Article', $result['title']);
        $this->assertEquals('Test Description', $result['full_content']);
    }

    public function testItParsesArticleWithMediaContent(): void {
        $rawContent        = '<item><title>Test Article</title><link>https://example.com/article</link><description>Test Description</description><content:encoded>Test Content</content:encoded><media:content url="https://example.com/image.jpg" type="image/jpeg" /></item>';
        $articleRepository = Mockery::mock(ArticleRepository::class);
        $contentHelper     = Mockery::mock(ContentHelper::class);
        $contentHelper->shouldReceive('getCanonicalUrl')
            ->andReturn('https://example.com/article');
        $articleRepository->shouldReceive('findByUrlAndContentMd5')
            ->andReturn(null);
        $extractor      = new ExtractsRssItemsFromRawContent($articleRepository, $contentHelper);
        $extractedItems = $extractor->execute($rawContent);

        $service = new ParsesArticleWithDefaultParser();
        $result  = $service->execute($extractedItems[0]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('title', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('full_content', $result);
        $this->assertEquals('Test Article', $result['title']);
        $this->assertEquals('https://example.com/image.jpg', $result['cover_image_url']['url']);
    }
}
