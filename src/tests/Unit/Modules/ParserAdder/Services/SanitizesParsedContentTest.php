<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\PublisherRepository;
use Exception;
use Mockery;
use Tests\TestCase;

class SanitizesParsedContentTest extends TestCase {
    public function testItSanitizesParsedContent(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentForUA(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher(['ga_id' => 'UA-123456789-1']);
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithLongDescription(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(300),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithNoFirstImage(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => '',
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => '',
                'caption' => null,
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithMoreThanTwoParagraphs(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>' . $this->generator->paragraph() . '</p><p>' . $this->generator->paragraph() . '</p><p>' . $this->generator->paragraph() . '</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()

            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItThrowsRuntimeExceptionOnFailure(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andThrow(new Exception('Database error'));
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);

        $service = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);

        $this->expectException(ServiceException::class);
        $service->execute($parsedContent, $mockPublisher->id, 1);
    }

    public function testItProcessesImagesInContentWithProxyImageEnabled(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image.jpg" alt="test"><p>More content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(true);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertStringContainsString('https://imgproxy.newswav.com/1000x0,q50=/https://example.com/image.jpg', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
    }

    public function testItProcessesImagesInContentWithProxyImageDisabled(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image.jpg" alt="test"><p>More content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertStringContainsString('https://example.com/image.jpg', $sanitizedContent['full_content']);
        $this->assertStringNotContainsString('https://imgproxy.newswav.com', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
    }

    public function testItProcessesMultipleImagesInContent(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image1.jpg" alt="test1"><p>More content</p><img src="https://example.com/image2.jpg" alt="test2"><p>End content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(true);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertStringContainsString('https://imgproxy.newswav.com/1000x0,q50=/https://example.com/image1.jpg', $sanitizedContent['full_content']);
        $this->assertStringContainsString('https://imgproxy.newswav.com/1000x0,q50=/https://example.com/image2.jpg', $sanitizedContent['full_content']);

        // Check for sequential image numbering
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'2\')"', $sanitizedContent['full_content']);
    }

    public function testItDoesNotModifyAlreadyProxiedImages(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://imgproxy.newswav.com/1000x0,q50=/https://example.com/image.jpg" alt="test"><p>More content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(true);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // Should not double-proxy the image
        $this->assertStringNotContainsString('https://imgproxy.newswav.com/1000x0,q50=/https://imgproxy.newswav.com', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
    }

    public function testItStartsImageCounterAtZeroWhenCoverImageNotAdded(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image1.jpg" alt="test1"><p>More content</p><img src="https://example.com/image2.jpg" alt="test2"><p>End content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => 'https://example.com/image1.jpg', // Same URL as in content, so cover image won't be added
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // When cover image is not added, counter should start at 0
        $this->assertStringContainsString('onClick="showImageDetail(\'0\')"', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
    }

    public function testItStartsImageCounterAtOneWhenCoverImageIsAdded(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image1.jpg" alt="test1"><p>More content</p><img src="https://example.com/image2.jpg" alt="test2"><p>End content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => 'https://example.com/cover.jpg', // Different URL, so cover image will be added
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')

            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // When cover image is added, counter should start at 1 (cover image gets position 0)
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
        $this->assertStringContainsString('onClick="showImageDetail(\'2\')"', $sanitizedContent['full_content']);
    }

    public function testItSanitizesParsedContentWithGA4Tracking(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher(['ga_id' => 'G-123456789']);
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
        $this->assertStringContainsString('ga4-track.html', $sanitizedContent['full_content']);
        $this->assertStringContainsString('ga4-v2-track.html', $sanitizedContent['full_content']);
    }

    public function testItEnsuresContentWrappedInParagraphWithEmptyContent(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // Even empty content gets Android script and potential GA tracking
        $this->assertStringContainsString('<script type="text/javascript">function showImageDetail(position) { Android.showImage(position); }</script>', $sanitizedContent['full_content']);
    }

    public function testItHandlesImagesWithExistingOnClickAttribute(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img src="https://example.com/image.jpg" onclick="existingHandler()" alt="test"><p>More content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertStringContainsString('onclick="existingHandler()"', $sanitizedContent['full_content']);
        $this->assertStringNotContainsString('onClick="showImageDetail(', $sanitizedContent['full_content']);
    }

    public function testItProcessesCoverImageWithProxyEnabled(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => 'https://example.com/cover.jpg',
                'caption' => 'Cover image caption',
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(true);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertStringContainsString('https://imgproxy.newswav.com/1000x0,q50=/https://example.com/cover.jpg', $sanitizedContent['full_content']);
        $this->assertStringContainsString('https://imgproxy.newswav.com/400x0,q50=/https://example.com/cover.jpg 400w', $sanitizedContent['full_content']);
        $this->assertStringContainsString('Cover image caption', $sanitizedContent['full_content']);
    }

    public function testItSanitizesLinkWithExistingQueryParameters(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => 'https://example.com/article?param=value',
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('link', $sanitizedContent);
        $this->assertStringContainsString('&utm_source=Newswav&utm_medium=App', $sanitizedContent['link']);
    }

    public function testItHandlesHtmlEntitiesInContent(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '&lt;p&gt;HTML entity content&lt;/p&gt;',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // HTML entities should be decoded in ensureContentWrappedInParagraph
        $this->assertStringContainsString('<p>HTML entity content</p>', $sanitizedContent['full_content']);
    }

    public function testItHandlesNullPublisher(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $publisherId                    = 999;
        $publisherRepositoryMock        = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($publisherId)
            ->once()
            ->andReturn(null);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($publisherId, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $publisherId, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // Should not contain GA tracking when publisher is null
        $this->assertStringNotContainsString('ga-id=', $sanitizedContent['full_content']);
    }

    public function testItHandlesImageWithoutSrcAttribute(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Some content</p><img alt="test without src"><p>More content</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // Image without src should still get onClick handler
        $this->assertStringContainsString('onClick="showImageDetail(\'1\')"', $sanitizedContent['full_content']);
    }

    public function testItHandlesContentWithParagraphTagsInOriginalButNotDecoded(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => '<p>Content with paragraph tags already present</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $publisherEndpointRepositoryMock->shouldReceive('getHasProxyImage')
            ->with($mockPublisher->id, 1)
            ->once()
            ->andReturn(false);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $publisherEndpointRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id, 1);

        $this->assertArrayHasKey('full_content', $sanitizedContent);
        // Should return the decoded content when p tags are already present
        $this->assertStringContainsString('<p>Content with paragraph tags already present</p>', $sanitizedContent['full_content']);
    }
}
