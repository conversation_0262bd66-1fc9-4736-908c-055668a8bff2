<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Exceptions\InvalidAiResponseException;
use App\Modules\ParserAdder\Services\ExtractsContentArrayFromAiResponse;
use Tests\TestCase;

class ExtractsArrayFromAiResponseTest extends TestCase {
    public function testItExtractsArrayFromAiResponse(): void {
        $aiResponse = '```json
        [
            {
                "title": "Test",
                "description": "Test"
            }
        ]
        ```';

        $service = new ExtractsContentArrayFromAiResponse();
        $result  = $service->execute($aiResponse);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
    }

    public function testItExtractsArrayFromAiResponseWithoutJsonTag(): void {
        $aiResponse = '[
            {
                "title": "Test",
                "description": "Test"
            }
        ]';

        $service = new ExtractsContentArrayFromAiResponse();
        $result  = $service->execute($aiResponse);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
    }

    public function testItExtractsArrayFromAiResponseWithInvalidJson(): void {
        $this->expectException(InvalidAiResponseException::class);

        $aiResponse = '[
            {
                "title": "Test, "",
                "description": "Test"

        ]';

        $service = new ExtractsContentArrayFromAiResponse();
        $service->execute($aiResponse);
    }
}
