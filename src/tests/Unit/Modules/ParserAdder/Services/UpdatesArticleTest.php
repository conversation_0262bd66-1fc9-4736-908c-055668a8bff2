<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleObject;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;

class UpdatesArticleTest extends TestCase {
    public function testItUpdatesArticle(): void {
        $articleObject         = Mockery::mock(ArticleObject::class);
        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('updateArticleFromObject')
            ->once()
            ->andReturn(true);

        $service = new UpdatesArticle($articleRepositoryMock);
        $service->execute($articleObject);
    }
}
