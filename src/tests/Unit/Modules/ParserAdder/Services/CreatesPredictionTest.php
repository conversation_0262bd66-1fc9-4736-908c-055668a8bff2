<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Classes\ValueObjects\MediaWithThumbnailDataObject;
use App\Models\Thumbnail;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Repositories\PredictionsRepository;
use Mockery;
use Tests\TestCase;

class CreatesPredictionTest extends TestCase {
    public function testItCreatePrediction(): void {

        $channel = $this->createChannel([
            'language' => 'en',
        ]);

        $mockArticle = $this->createArticle([
            'channelID' => $channel->id,
            'media'     => '123,456', // Add media string with two media IDs
        ]);

        $expectedPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('getArticleDataWithPredictionData')
            ->with($mockArticle->uniqueID)
            ->once()
            ->andReturn(null);
        $predictionsRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($expectedPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $result  = $service->execute($mockArticle);

        $this->assertInstanceOf(ArticleDataWithPredictionDataObject::class, $result);
    }

    public function testItDoesNotCreatePredictionWhenPredictionExists(): void {
        $mockArticle        = $this->createArticle();
        $existingPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('getArticleDataWithPredictionData')
            ->with($mockArticle->uniqueID)
            ->once()
            ->andReturn($existingPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $result  = $service->execute($mockArticle);

        $this->assertInstanceOf(ArticleDataWithPredictionDataObject::class, $result);
    }

    public function testItCreatePredictionWithMediaThumbnails(): void {
        $channel = $this->createChannel([
            'language' => 'en',
        ]);

        $mockArticle = $this->createArticle([
            'channelID' => $channel->id,
            'media'     => '123,456',
        ]);

        $expectedPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        $mockThumbnail          = Mockery::mock(Thumbnail::class);
        $mediaWithThumbnailMock = Mockery::mock(MediaWithThumbnailDataObject::class);
        $mediaWithThumbnailMock->shouldReceive('getThumbnail')
            ->andReturn($mockThumbnail);
        $mediaWithThumbnailMock->shouldReceive('getThumbnailURL')
            ->andReturn('https://example.com/thumbnail.jpg');
        $mediaWithThumbnailMock->shouldReceive('getWideImageWidth')
            ->andReturn(1200);
        $mediaWithThumbnailMock->shouldReceive('getWideImageHeight')
            ->andReturn(800);

        $mediaWithThumbnails = [$mediaWithThumbnailMock];

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('getArticleDataWithPredictionData')
            ->with($mockArticle->uniqueID)
            ->once()
            ->andReturn(null);
        $predictionsRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($expectedPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $result  = $service->execute($mockArticle, $mediaWithThumbnails);

        $this->assertInstanceOf(ArticleDataWithPredictionDataObject::class, $result);
        // Verify that thumbnail data was set on the prediction
        $this->assertEquals('https://example.com/thumbnail.jpg', $expectedPrediction->thumbnailURL);
        $this->assertEquals(1200, $expectedPrediction->wideImageWidth);
        $this->assertEquals(800, $expectedPrediction->wideImageHeight);
    }

    public function testItCreatePredictionWithVideosFirst(): void {
        $channel = $this->createChannel([
            'language' => 'en',
        ]);

        $mockArticle = $this->createArticle([
            'channelID' => $channel->id,
            'media'     => '123,456,789',
        ]);

        $expectedPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        // Mock video (first item - no thumbnail)
        $videoMock = Mockery::mock(MediaWithThumbnailDataObject::class);
        $videoMock->shouldReceive('getThumbnail')
            ->andReturn(null);

        // Mock image (second item - has thumbnail)
        $mockThumbnail = Mockery::mock(Thumbnail::class);
        $imageMock     = Mockery::mock(MediaWithThumbnailDataObject::class);
        $imageMock->shouldReceive('getThumbnail')
            ->andReturn($mockThumbnail);
        $imageMock->shouldReceive('getThumbnailURL')
            ->andReturn('https://example.com/image-thumbnail.jpg');
        $imageMock->shouldReceive('getWideImageWidth')
            ->andReturn(800);
        $imageMock->shouldReceive('getWideImageHeight')
            ->andReturn(600);

        $mediaWithThumbnails = [$videoMock, $imageMock]; // Video first, image second

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('getArticleDataWithPredictionData')
            ->with($mockArticle->uniqueID)
            ->once()
            ->andReturn(null);
        $predictionsRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($expectedPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $result  = $service->execute($mockArticle, $mediaWithThumbnails);

        $this->assertInstanceOf(ArticleDataWithPredictionDataObject::class, $result);
        // Verify that image thumbnail data was used (not the video)
        $this->assertEquals('https://example.com/image-thumbnail.jpg', $expectedPrediction->thumbnailURL);
        $this->assertEquals(800, $expectedPrediction->wideImageWidth);
        $this->assertEquals(600, $expectedPrediction->wideImageHeight);
    }

    public function testItCreatePredictionWithOnlyVideos(): void {
        $channel = $this->createChannel([
            'language' => 'en',
        ]);

        $mockArticle = $this->createArticle([
            'channelID' => $channel->id,
            'media'     => '123,456',
        ]);

        $expectedPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        // Mock two videos (no thumbnails)
        $videoMock1 = Mockery::mock(MediaWithThumbnailDataObject::class);
        $videoMock1->shouldReceive('getThumbnail')
            ->andReturn(null);

        $videoMock2 = Mockery::mock(MediaWithThumbnailDataObject::class);
        $videoMock2->shouldReceive('getThumbnail')
            ->andReturn(null);

        $mediaWithThumbnails = [$videoMock1, $videoMock2]; // Only videos

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('getArticleDataWithPredictionData')
            ->with($mockArticle->uniqueID)
            ->once()
            ->andReturn(null);
        $predictionsRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($expectedPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $result  = $service->execute($mockArticle, $mediaWithThumbnails);

        $this->assertInstanceOf(ArticleDataWithPredictionDataObject::class, $result);
        // Verify that no thumbnail data was set since there are only videos
        // The prediction should not have thumbnail fields set
    }
}
