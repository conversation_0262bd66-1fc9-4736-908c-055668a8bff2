<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Repositories\ArticleWordCountRepository;
use Mockery;
use Tests\TestCase;

class PopulatesArticleWordCountTest extends TestCase {
    public function testItPopulatesArticleWordCount(): void {

        $mockArticleData = $this->generator->sentence();
        $mockUniqueId    = 'A2506_existing';
        $mockWordCount   = $this->createArticleWordCount(['unique_id' => $mockUniqueId]);

        $articleWordCountRepositoryMock = Mockery::mock(ArticleWordCountRepository::class);
        $articleWordCountRepositoryMock->shouldReceive('findWhere')
            ->with(['unique_id' => $mockUniqueId])
            ->once()
            ->andReturn(null);
        $articleWordCountRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($mockWordCount);

        $service = new PopulatesArticleWordCount($articleWordCountRepositoryMock);
        $service->execute($mockUniqueId, $mockArticleData);

        $this->assertTrue(true);
    }

    public function testItUpdatesArticleWordCount(): void {
        $mockArticleData = $this->generator->sentence();
        $mockUniqueId    = 'A2506_existing';
        $mockWordCount   = $this->createArticleWordCount(['unique_id' => $mockUniqueId]);

        $articleWordCountRepositoryMock = Mockery::mock(ArticleWordCountRepository::class);
        $articleWordCountRepositoryMock->shouldReceive('findWhere')
            ->with(['unique_id' => $mockUniqueId])
            ->once()
            ->andReturn($mockWordCount);
        $articleWordCountRepositoryMock->shouldReceive('update')
            ->once()
            ->andReturn(true);

        $service = new PopulatesArticleWordCount($articleWordCountRepositoryMock);
        $service->execute($mockUniqueId, $mockArticleData);

        $this->assertTrue(true);
    }
}
