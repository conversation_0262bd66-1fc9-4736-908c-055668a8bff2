<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\ParseArticleException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Logics\ParseArticleDataManuallyLogic;
use App\Modules\ParserAdder\Services\ParsesArticleContent;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;
use Tests\Traits\LoadsStubs;

class ParseArticleDataManuallyLogicTest extends TestCase {
    use LoadsStubs;

    public function testItParseArticleDataFromRss(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_rss_item'  => $rssContent,
            'use_ai_parsing'         => false,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($rssContent),
        ];

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($rssContent, Mockery::any(), false, false, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingHeadlessBrowser(): void {
        $htmlLink       = $this->generator->url();
        $cleanedRawData = '<raw html content>';

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => true,
            'to_puppeteer'         => false,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $headlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $headlessBrowserService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, Mockery::any(), true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            $headlessBrowserService,
            Mockery::mock(GuzzleReadService::class),
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingBypassCloudflare(): void {
        $htmlLink       = $this->generator->url();
        $cleanedRawData = '<raw html content>';

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => false,
            'to_puppeteer'         => true,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, Mockery::any(), true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            $bypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingGuzzleReadService(): void {
        $htmlLink       = $this->generator->url();
        $cleanedRawData = '<raw html content>';

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, Mockery::any(), true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $guzzleReadService,
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals($mockData, $result);
    }

    public function testItReturnsEmptyArrayWhenAiReturnsEmptyArray(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_rss_item'  => $rssContent,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id' => 1,
            'channel_id'   => 2,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->andReturn([]);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals([], $result);
    }

    public function testItReturnsEmptyArrayWhenInvalidAiResponseException(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_rss_item'  => $rssContent,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id' => 1,
            'channel_id'   => 2,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->andThrow(new \App\Exceptions\InvalidAiResponseException(500, 'Test exception', 'raw response', 'cleaned input'));

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($request);
        $this->assertEquals([], $result);
    }

    public function testItThrowsExceptionWhenNoCrawlerSettingFound(): void {
        $this->expectException(ParseArticleException::class);

        $request = new Request();
        $request->merge([
            'publisher_id' => 1,
            'channel_id'   => 2,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn(null);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $logic->execute($request);
    }

    public function testItReturnsEmptyArrayWhenNothingIsFetchedFromBypassCloudflare(): void {
        $htmlLink = $this->generator->url();

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => false,
            'to_puppeteer'         => true,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')->once()->andReturn(null);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            $bypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $result = $logic->execute($request);
        $this->assertEquals([], $result);
    }

    public function testItReturnsEmptyArrayWhenNothingIsFetchedFromGuzzleReadService(): void {
        $htmlLink = $this->generator->url();

        $request = new Request();
        $request->merge([
            'publisher_id'           => 1,
            'channel_id'             => 2,
            'article_data_html_link' => $htmlLink,
            'use_ai_parsing'         => true,
        ]);

        $mockCrawlerSetting = $this->createPublisherCrawlerSetting([
            'publisher_id'         => 1,
            'channel_id'           => 2,
            'use_headless_browser' => false,
            'to_puppeteer'         => false,
        ]);

        $mockRepository = Mockery::mock(PublisherCrawlerSettingRepository::class);
        $mockRepository->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockCrawlerSetting);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')->once()->andReturn(null);

        $logic = new ParseArticleDataManuallyLogic(
            $mockRepository,
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $guzzleReadService,
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $result = $logic->execute($request);
        $this->assertEquals([], $result);
    }
}
