<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Classes\ValueObjects\ArticleProcessingDataObject;
use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\ParseArticleException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Services\ParsesArticleContent;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Mockery;
use Tests\TestCase;
use Tests\Traits\LoadsStubs;

class ParseArticleDataLogicTest extends TestCase {
    use LoadsStubs;

    public function testItParseArticleDataFromRss(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '', false, false, $rssContent, null, true
        );

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($rssContent),
        ];

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($rssContent, '', true, false, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingHeadlessBrowser(): void {
        $cleanedRawData = '<raw html content>';

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '123', false, true, null, $this->generator->url(), true
        );

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $headlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $headlessBrowserService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, '123', true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            $headlessBrowserService,
            Mockery::mock(GuzzleReadService::class),
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingBypassCloudflare(): void {
        $cleanedRawData = '<raw html content>';

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '123', true, false, null, $this->generator->url(), true
        );

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, '123', true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            $bypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingGuzzleReadService(): void {
        $cleanedRawData = '<raw html content>';

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '123', false, false, null, $this->generator->url(), true
        );

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($cleanedRawData),
        ];

        $rawContentObjectMock = Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('cleanRawData')->once()->andReturn($cleanedRawData);

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($cleanedRawData, '123', true, true, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $guzzleReadService,
            $mockContentHelper,
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals($mockData, $result);
    }

    public function testItParsesArticledataFromRssWithDefaultParser(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '', false, false, $rssContent, null, false
        );

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
            'content_md5'     => md5($rssContent),
        ];

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->with($rssContent, '', false, false, 1, 2)
            ->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals($mockData, $result);
    }

    public function testItParseArticleDataThrowsBadRequestException(): void {
        $this->expectException(ParseArticleException::class);

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '', false, false, null, null, true
        );

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $logic->execute($fakeArticleProcessingDataObject);
    }

    public function testItParseArticleDataThrowsInternalServerException(): void {
        $this->expectException(ParseArticleException::class);

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '', false, false, null, $this->generator->url(), true
        );

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $logic->execute($fakeArticleProcessingDataObject);
    }

    public function testItReturnsEmptyArrayWhenInvalidAiResponseException(): void {
        $rssContent = $this->getRssItemStubWithGuid();

        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '', false, false, $rssContent, null, true
        );

        $mockParsesArticleContent = Mockery::mock(ParsesArticleContent::class);
        $mockParsesArticleContent->shouldReceive('execute')
            ->once()
            ->andThrow(new \App\Exceptions\InvalidAiResponseException(500, 'Test exception', 'raw response', 'cleaned input'));

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            $mockParsesArticleContent
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals([], $result);
    }

    public function testItReturnsEmptyArrayWhenNothingIsFetchedFromBypassCloudflare(): void {
        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '123', true, false, null, $this->generator->url(), true
        );

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')->once()->andReturn(null);

        $logic = new ParseArticleDataLogic(
            $bypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(GuzzleReadService::class),
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals([], $result);
    }

    public function testItReturnsEmptyArrayWhenNothingIsFetchedFromGuzzleReadService(): void {
        $fakeArticleProcessingDataObject = new ArticleProcessingDataObject(
            1, 2, '123', false, false, null, $this->generator->url(), true
        );

        $guzzleReadService = Mockery::mock(GuzzleReadService::class);
        $guzzleReadService->shouldReceive('getRawHtmlOrRss')->once()->andReturn(null);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $guzzleReadService,
            Mockery::mock(ContentHelper::class),
            Mockery::mock(ParsesArticleContent::class)
        );

        $result = $logic->execute($fakeArticleProcessingDataObject);
        $this->assertEquals([], $result);
    }
}
