<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\AiModels;

use App\Classes\ValueObjects\GeminiAiModelObject;
use App\Classes\ValueObjects\OpenAiModelObject;
use App\Exceptions\AiClientException;
use App\Modules\AiModels\AiModelClient;
use App\Modules\AiModels\GeminiAccessTokenProvider;
use App\Modules\AiModels\Services\LogsAiModelEstimatedCost;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class AiModelClientTest extends TestCase {
    public function testItAskSuccessWithGemini(): void {
        $fakeUserPrompt     = $this->generator->text();
        $fakeSystemPrompt   = $this->generator->text();
        $fakePromptResponse = $this->generator->text();
        $fakeResponse       = new Response(200, [], json_encode([
            'candidates' => [
                [
                    'content' => [
                        'parts' => [
                            ['text' => $fakePromptResponse],
                        ],
                    ],
                ],
            ],
            'usageMetadata' => [
                'promptTokenCount'     => 10,
                'candidatesTokenCount' => 20,
            ],
        ]));

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $geminiTokenProviderMock = Mockery::mock(GeminiAccessTokenProvider::class);
        $geminiTokenProviderMock->shouldReceive('execute')
            ->once()
            ->andReturn('fake-gemini-token');

        $logsAiModelEstimatedCostMock = Mockery::mock(LogsAiModelEstimatedCost::class);
        $logsAiModelEstimatedCostMock->shouldReceive('execute')
            ->once()
            ->with(
                Mockery::type(GeminiAiModelObject::class),
                10,
                20
            );

        $service = new AiModelClient(
            $clientMock,
            $logsAiModelEstimatedCostMock,
            $geminiTokenProviderMock
        );

        $result = $service->ask($fakeSystemPrompt, $fakeUserPrompt);
        $this->assertEquals($fakePromptResponse, $result);
    }

    public function testItAskFallsBackToGptWhenGeminiFails(): void {
        $fakeUserPrompt     = $this->generator->text();
        $fakeSystemPrompt   = $this->generator->text();
        $fakePromptResponse = $this->generator->text();
        $fakeGptResponse    = new Response(200, [], json_encode([
            'choices' => [
                [
                    'message' => [
                        'content' => $fakePromptResponse,
                    ],
                ],
            ],
            'usage' => [
                'prompt_tokens'     => 15,
                'completion_tokens' => 25,
            ],
        ]));

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->times(3)
            ->andReturnUsing(function ($method, $url, $args) use ($fakeGptResponse) {
                if (str_contains($url, 'openai') || str_contains($url, 'chat/completions')) {
                    return $fakeGptResponse;
                }

                throw new Exception('');
            });

        $geminiTokenProviderMock = Mockery::mock(GeminiAccessTokenProvider::class);
        $geminiTokenProviderMock->shouldReceive('execute')
            ->once()
            ->andReturn('fake-gemini-token');

        $logsAiModelEstimatedCostMock = Mockery::mock(LogsAiModelEstimatedCost::class);
        $logsAiModelEstimatedCostMock->shouldReceive('execute')
            ->once()
            ->with(
                Mockery::type(OpenAiModelObject::class),
                15,
                25
            );

        $service = new AiModelClient(
            $clientMock,
            $logsAiModelEstimatedCostMock,
            $geminiTokenProviderMock
        );

        $result = $service->ask($fakeSystemPrompt, $fakeUserPrompt);
        $this->assertEquals($fakePromptResponse, $result);
    }

    public function testItAskThrowsExceptionWhenBothModelsFail(): void {
        $this->expectException(AiClientException::class);

        $fakeUserPrompt   = $this->generator->text();
        $fakeSystemPrompt = $this->generator->text();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->times(4)
            ->andThrow(new Exception(''));

        $geminiTokenProviderMock = Mockery::mock(GeminiAccessTokenProvider::class);
        $geminiTokenProviderMock->shouldReceive('execute')
            ->once()
            ->andReturn('fake-gemini-token');

        $logsAiModelEstimatedCostMock = Mockery::mock(LogsAiModelEstimatedCost::class);

        $service = new AiModelClient(
            $clientMock,
            $logsAiModelEstimatedCostMock,
            $geminiTokenProviderMock
        );

        $service->ask($fakeSystemPrompt, $fakeUserPrompt);
    }
}
