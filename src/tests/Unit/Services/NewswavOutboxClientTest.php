<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Exceptions\APIException;
use App\Services\NewswavOutboxClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class NewswavOutboxClientTest extends TestCase {
    public function testItEmitsMessage(): void {
        $topic      = 'test-topic';
        $keyField   = 'hostname';
        $eventName  = 'test-event';
        $clientMock = Mockery::mock(Client::class);

        $mediaMock     = $this->createMedia();
        $channelMock   = $this->createChannel();
        $publisherMock = $this->createPublisher();
        $articleMock   = $this->createArticle([
            'channelID' => $channelMock->id,
            'media'     => $mediaMock->id,
        ]);
        $predictionMock = $this->createPrediction([
            'unique_id'    => $articleMock->uniqueID,
            'publisher_id' => $publisherMock->id,
        ]);

        $articleDataWithPredictionDataObject = Mockery::mock(ArticleDataWithPredictionDataObject::class);
        $articleDataWithPredictionDataObject->shouldReceive('getPrediction')->andReturn($predictionMock);
        $articleDataWithPredictionDataObject->shouldReceive('getArticle')->andReturn($articleMock);
        $articleDataWithPredictionDataObject->shouldReceive('getChannel')->andReturn($channelMock);
        $articleDataWithPredictionDataObject->shouldReceive('getPublisher')->andReturn($publisherMock);
        $articleDataWithPredictionDataObject->shouldReceive('getThumbnailURL')->andReturn('https://example.com/wide.jpg');
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageWidth')->andReturn(1920);
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageHeight')->andReturn(1080);

        $fakeResponse = new Response(200, [], '');
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new NewswavOutboxClient($clientMock);
        $service->emitMessage($topic, $articleDataWithPredictionDataObject, $keyField, $eventName);
        $this->assertTrue(true);
    }

    public function testItEmitMessageWithKey(): void {
        $topic      = 'test-topic';
        $keyField   = 'contentId';
        $eventName  = 'test-event';
        $clientMock = Mockery::mock(Client::class);

        $mediaMock     = $this->createMedia();
        $channelMock   = $this->createChannel();
        $publisherMock = $this->createPublisher();
        $articleMock   = $this->createArticle([
            'channelID' => $channelMock->id,
            'media'     => $mediaMock->id,
        ]);
        $predictionMock = $this->createPrediction([
            'unique_id'    => $articleMock->uniqueID,
            'publisher_id' => $publisherMock->id,
        ]);

        $articleDataWithPredictionDataObject = Mockery::mock(ArticleDataWithPredictionDataObject::class);
        $articleDataWithPredictionDataObject->shouldReceive('getPrediction')->andReturn($predictionMock);
        $articleDataWithPredictionDataObject->shouldReceive('getArticle')->andReturn($articleMock);
        $articleDataWithPredictionDataObject->shouldReceive('getChannel')->andReturn($channelMock);
        $articleDataWithPredictionDataObject->shouldReceive('getPublisher')->andReturn($publisherMock);
        $articleDataWithPredictionDataObject->shouldReceive('getThumbnailURL')->andReturn('https://example.com/wide.jpg');
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageWidth')->andReturn(1920);
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageHeight')->andReturn(1080);

        $fakeResponse = new Response(200, [], '');
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new NewswavOutboxClient($clientMock);
        $service->emitMessage($topic, $articleDataWithPredictionDataObject, $keyField, $eventName);
        $this->assertTrue(true);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(APIException::class);
        $topic         = 'test-topic';
        $keyField      = 'test';
        $eventName     = 'test-event';
        $mediaMock     = $this->createMedia();
        $channelMock   = $this->createChannel();
        $publisherMock = $this->createPublisher();
        $articleMock   = $this->createArticle([
            'channelID' => $channelMock->id,
            'media'     => $mediaMock->id,
        ]);
        $predictionMock = $this->createPrediction([
            'unique_id'    => $articleMock->uniqueID,
            'publisher_id' => $publisherMock->id,
        ]);

        $articleDataWithPredictionDataObject = Mockery::mock(ArticleDataWithPredictionDataObject::class);
        $articleDataWithPredictionDataObject->shouldReceive('getPrediction')->andReturn($predictionMock);
        $articleDataWithPredictionDataObject->shouldReceive('getArticle')->andReturn($articleMock);
        $articleDataWithPredictionDataObject->shouldReceive('getChannel')->andReturn($channelMock);
        $articleDataWithPredictionDataObject->shouldReceive('getPublisher')->andReturn($publisherMock);
        $articleDataWithPredictionDataObject->shouldReceive('getThumbnailURL')->andReturn('https://example.com/wide.jpg');
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageWidth')->andReturn(1920);
        $articleDataWithPredictionDataObject->shouldReceive('getWideImageHeight')->andReturn(1080);

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new RequestException('Guzzle error', new Request('POST', 'test')));

        $service = new NewswavOutboxClient($clientMock);
        $service->emitMessage($topic, $articleDataWithPredictionDataObject, $keyField, $eventName);
    }
}
