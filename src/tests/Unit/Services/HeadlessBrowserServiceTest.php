<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\HeadlessBrowserService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class HeadlessBrowserServiceTest extends TestCase {
    public function testItGetRawHtmlOrRss(): void {

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new HeadlessBrowserService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
        $this->assertEquals($fakeResponse->getBody(), $result->getRawContent());
    }

    public function testItThrowsExceptionAtEmptyContent(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new HeadlessBrowserService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new RequestException('failed', new Request('500', 'Failed')));

        $service = new HeadlessBrowserService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItReturnsNullOnTimeout(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new ConnectException('timeout', new Request('POST', 'test')));

        $service = new HeadlessBrowserService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
        $this->assertNull($result);
    }
}
