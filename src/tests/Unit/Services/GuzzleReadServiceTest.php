<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\GuzzleReadService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class GuzzleReadServiceTest extends TestCase {
    public function testItGetRawHtmlOrRss(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new GuzzleReadService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertEquals('<html><body><h1>Test</h1></body></html>', $result->getRawContent());
    }

    public function testItGetRawHtmlOrRssWithDefaultUserAgent(): void {
        $endpointUrl = $this->generator->url();

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new GuzzleReadService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl);

        $this->assertEquals('<html><body><h1>Test</h1></body></html>', $result->getRawContent());
    }

    public function testItThrowsExceptionAtEmptyContent(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new GuzzleReadService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItReturnsNullOnTimeout(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new ConnectException('Connection timeout', new Request('GET', $endpointUrl)));

        $service = new GuzzleReadService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertNull($result);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new RequestException('Bad request', new Request('GET', $endpointUrl)));

        $service = new GuzzleReadService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItReturnsNullOnServerError503(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response = new Response(503, [], 'Service Unavailable');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ServerException('Service Unavailable', new Request('GET', $endpointUrl), $response));

        $service = new GuzzleReadService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertNull($result);
    }

    public function testItReturnsNullOnClientError404(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response = new Response(404, [], 'Not Found');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ClientException('Not Found', new Request('GET', $endpointUrl), $response));

        $service = new GuzzleReadService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertNull($result);
    }
}
