<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Exceptions\APIException;
use App\Services\ParserAdderClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class ParserAdderClientTest extends TestCase {
    public function testItPostsRssArticleDataToParserAdderService(): void {
        $publisherId        = $this->generator->numberBetween(1, 1000);
        $channelId          = $this->generator->numberBetween(1, 1000);
        $articleData        = $this->generator->sentence();
        $customPrompt       = $this->generator->sentence();
        $useHeadlessBrowser = $this->generator->boolean();
        $isRss              = true;

        $fakeResponse = new Response(200, [], '');
        $clientMock   = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new ParserAdderClient($clientMock);
        $service->postArticleDataToParserAdderService($publisherId, $channelId, $articleData, $isRss, $customPrompt, $useHeadlessBrowser);
    }

    public function testItPostsHtmlArticleDataToParserAdderService(): void {
        $publisherId        = $this->generator->numberBetween(1, 1000);
        $channelId          = $this->generator->numberBetween(1, 1000);
        $articleData        = $this->generator->url();
        $customPrompt       = $this->generator->sentence();
        $useHeadlessBrowser = $this->generator->boolean();
        $isRss              = false;

        $fakeResponse = new Response(200, [], '');
        $clientMock   = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new ParserAdderClient($clientMock);
        $service->postArticleDataToParserAdderService($publisherId, $channelId, $articleData, $isRss, $customPrompt, $useHeadlessBrowser);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(APIException::class);
        $publisherId        = $this->generator->numberBetween(1, 1000);
        $channelId          = $this->generator->numberBetween(1, 1000);
        $articleData        = $this->generator->sentence();
        $isRss              = $this->generator->boolean();
        $customPrompt       = $this->generator->sentence();
        $useHeadlessBrowser = $this->generator->boolean();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new RequestException('Guzzle error', new Request('POST', 'test')));

        $service = new ParserAdderClient($clientMock);
        $service->postArticleDataToParserAdderService($publisherId, $channelId, $articleData, $isRss, $customPrompt, $useHeadlessBrowser);
    }
}
