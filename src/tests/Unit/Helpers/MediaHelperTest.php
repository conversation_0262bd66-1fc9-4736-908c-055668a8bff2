<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\MediaHelper;
use Error;
use Exception;
use PHPUnit\Framework\TestCase;
use TypeError;

class MediaHelperTest extends TestCase {
    public function testItIsImageUrl(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.jpg'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.jpeg'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.png'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.gif'));
        $this->assertTrue($mediaHelper->isImageUrl('https://example.com/image.webp'));

        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mp4'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mov'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.avi'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.wmv'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.flv'));
        $this->assertFalse($mediaHelper->isImageUrl('https://example.com/image.mkv'));
    }

    public function testItIsVideoUrl(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mp4'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mov'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.avi'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.wmv'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.flv'));
        $this->assertTrue($mediaHelper->isVideoUrl('https://example.com/video.mkv'));

        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.jpg'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.jpeg'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.png'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.gif'));
        $this->assertFalse($mediaHelper->isVideoUrl('https://example.com/video.webp'));
    }

    public function testItGetImageSize(): void {
        $mediaHelper = new MediaHelper();

        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.jpg'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.jpeg'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.png'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.webp'));
        $this->assertEquals(['width' => 600, 'height' => 400], $mediaHelper->getImageSize('https://placehold.co/600x400.gif'));
    }

    public function testItIsMediaUrlValid(): void {
        $mediaHelper = new MediaHelper();

        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.jpg'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.jpeg'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.png'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.webp'));
        $this->assertTrue($mediaHelper->isMediaUrlValid('https://placehold.co/600x400.gif'));
    }

    public function testGetImageSizeWhenCurlExecReturnsFalse(): void {
        $mediaHelper = new MediaHelper();
        $this->assertEquals(['width' => 0, 'height' => 0], $mediaHelper->getImageSize('https://invalid-url.com/image.jpg'));
    }

    public function testItMediaUrlInvalid(): void {
        $mediaHelper = new MediaHelper();

        $this->assertFalse($mediaHelper->isMediaUrlValid('htt://invalid-url.com/image.jpg'));
        $this->assertFalse($mediaHelper->isMediaUrlValid('file:///local/path/image.jpg'));
        $this->assertFalse($mediaHelper->isMediaUrlValid('mailto:<EMAIL>'));
        $this->assertFalse($mediaHelper->isMediaUrlValid('/relative/path/image.jpg'));
        $this->assertFalse($mediaHelper->isMediaUrlValid('www.example.com/image.jpg'));
        $this->assertFalse($mediaHelper->isMediaUrlValid('example.com/image.jpg'));
    }

    public function testGetImageSizeHandlesThrowableException(): void {
        $helper = new class extends MediaHelper {
            protected function fetchImageData(string $imageUrl): bool | string {
                throw new Error('Simulated error in fetchImageData');
            }
        };

        $result = $helper->getImageSize('https://example.com/image.jpg');
        $this->assertEquals(['width' => 0, 'height' => 0], $result);
    }

    public function testGetImageSizeHandlesExceptionInGetImageSizeFromData(): void {
        $helper = new class extends MediaHelper {
            protected function fetchImageData(string $imageUrl): bool | string {
                return 'some-image-data';
            }

            protected function getImageSizeFromData($imageData): false | array {
                throw new TypeError('Simulated error in getImageSizeFromData');
            }
        };

        $result = $helper->getImageSize('https://example.com/image.jpg');
        $this->assertEquals(['width' => 0, 'height' => 0], $result);
    }

    public function testIsMediaUrlValidHandlesException(): void {
        $helper = new class extends MediaHelper {
            protected function checkUrlResponseCode(string $url): int {
                throw new Exception('Simulated exception in checkUrlResponseCode');
            }
        };

        $result = $helper->isMediaUrlValid('https://example.com/image.jpg');
        $this->assertFalse($result);
    }

    public function testGetImageSizeHandlesInvalidImageSizeData(): void {
        $helper = new class extends MediaHelper {
            protected function fetchImageData(string $imageUrl): bool | string {
                return 'some-valid-image-data';
            }

            protected function getImageSizeFromData($imageData): false | array {
                return ['mime' => 'image/jpeg'];
            }
        };

        $result = $helper->getImageSize('https://example.com/image.jpg');
        $this->assertEquals(['width' => 0, 'height' => 0], $result);
    }

    public function testGetImageSizeHandlesEmptyImageData(): void {
        $helper = new class extends MediaHelper {
            protected function fetchImageData(string $imageUrl): bool | string {
                return '';
            }
        };

        $result = $helper->getImageSize('https://example.com/image.jpg');
        $this->assertEquals(['width' => 0, 'height' => 0], $result);
    }

    public function testGetImageSizeHandlesFalseImageData(): void {
        $helper = new class extends MediaHelper {
            protected function fetchImageData(string $imageUrl): bool | string {
                return false;
            }
        };

        $result = $helper->getImageSize('https://example.com/image.jpg');
        $this->assertEquals(['width' => 0, 'height' => 0], $result);
    }
}
