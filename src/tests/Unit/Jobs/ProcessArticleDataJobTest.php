<?php

declare(strict_types=1);

namespace Tests\Unit\Jobs;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleProcessingDataObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Jobs\ProcessArticleDataJob;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use Mockery;
use Tests\TestCase;
use Tests\Traits\FakeQueuesEvents;

class ProcessArticleDataJobTest extends TestCase {
    use FakeQueuesEvents;

    public function testJobHandlesArticleProcessingCorrectly(): void {
        $parsedArticleData = [
            Parser::ARTICLE_ID      => $this->generator->randomNumber(),
            Parser::TITLE           => $this->generator->text(),
            Parser::DESCRIPTION     => $this->generator->text(),
            Parser::FULL_CONTENT    => $this->generator->text(),
            Parser::AUTHOR          => $this->generator->name(),
            Parser::PUBLISHED_DATE  => $this->generator->date(),
            Parser::MODIFIED_DATE   => $this->generator->date(),
            Parser::LINK            => $this->generator->url(),
            Parser::CANONICAL_URL   => $this->generator->url(),
            Parser::COVER_IMAGE_URL => ['url' => 'https://example.com/image.jpg', 'caption' => 'Test image'],
            Parser::MEDIA           => [],
            Parser::CONTENT_MD5     => 'test-md5-hash',
        ];

        $articleProcessingData = Mockery::mock(ArticleProcessingDataObject::class);
        $articleProcessingData->shouldReceive('getPublisherId')->andReturn(1);
        $articleProcessingData->shouldReceive('getChannelId')->andReturn(2);

        $parseArticleDataLogic = Mockery::mock(ParseArticleDataLogic::class);
        $parseArticleDataLogic->shouldReceive('execute')
            ->once()
            ->with($articleProcessingData)
            ->andReturn($parsedArticleData);

        $populateArticleDataIntoDatabaseLogic = Mockery::mock(PopulateArticleDataIntoDatabaseLogic::class);
        $populateArticleDataIntoDatabaseLogic->shouldReceive('execute')
            ->once()
            ->withArgs(function (ParsedArticleObject $parsedArticleObject, int $publisherId, int $channelId) use ($parsedArticleData) {
                // Verify that the ParsedArticleObject was created correctly with the parsed data
                $this->assertEquals($parsedArticleData[Parser::ARTICLE_ID], $parsedArticleObject->getArticleId());
                $this->assertEquals($parsedArticleData[Parser::TITLE], $parsedArticleObject->getTitle());
                $this->assertEquals($parsedArticleData[Parser::DESCRIPTION], $parsedArticleObject->getDescription());
                $this->assertEquals($parsedArticleData[Parser::FULL_CONTENT], $parsedArticleObject->getFullContent());
                $this->assertEquals($parsedArticleData[Parser::AUTHOR], $parsedArticleObject->getAuthor());
                $this->assertEquals($parsedArticleData[Parser::PUBLISHED_DATE], $parsedArticleObject->getPublishedDate());
                $this->assertEquals($parsedArticleData[Parser::MODIFIED_DATE], $parsedArticleObject->getModifiedDate());
                $this->assertEquals($parsedArticleData[Parser::LINK], $parsedArticleObject->getUrl());
                $this->assertEquals($parsedArticleData[Parser::CANONICAL_URL], $parsedArticleObject->getCanonicalURL());
                $this->assertEquals($parsedArticleData[Parser::COVER_IMAGE_URL], $parsedArticleObject->getCoverImage());
                $this->assertEquals($parsedArticleData[Parser::MEDIA], $parsedArticleObject->getMedia());
                $this->assertEquals($parsedArticleData[Parser::CONTENT_MD5], $parsedArticleObject->getContentMd5());

                // Verify publisher and channel IDs
                $this->assertEquals(1, $publisherId);
                $this->assertEquals(2, $channelId);

                return true;
            })
            ->andReturn('unique-id-123');

        $job = new ProcessArticleDataJob($articleProcessingData);
        $job->handle($parseArticleDataLogic, $populateArticleDataIntoDatabaseLogic);
    }

    public function testJobHandlesArticleProcessingCorrectlyWhenNoArticleDataParsed(): void {
        $articleProcessingData = Mockery::mock(ArticleProcessingDataObject::class);
        $articleProcessingData->shouldReceive('getPublisherId')->andReturn(1);
        $articleProcessingData->shouldReceive('getChannelId')->andReturn(2);

        $parseArticleDataLogic = Mockery::mock(ParseArticleDataLogic::class);
        $parseArticleDataLogic->shouldReceive('execute')
            ->once()
            ->with($articleProcessingData)
            ->andReturn([]);

        $populateArticleDataIntoDatabaseLogic = Mockery::mock(PopulateArticleDataIntoDatabaseLogic::class);
        $populateArticleDataIntoDatabaseLogic->shouldNotReceive('execute');

        $job = new ProcessArticleDataJob($articleProcessingData);
        $job->handle($parseArticleDataLogic, $populateArticleDataIntoDatabaseLogic);
    }
}
