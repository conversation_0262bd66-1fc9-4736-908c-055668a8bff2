<?php

declare(strict_types=1);

namespace Tests;

use Faker\Factory;
use Faker\Generator;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Tests\Traits\TestTraits;

abstract class TestCase extends BaseTestCase {
    use CreatesApplication;
    use TestTraits;
    use DatabaseTransactions;

    protected ?Generator $generator;

    public function setUp(): void {
        parent::setUp();
        $this->generator = Factory::create();
    }
}
