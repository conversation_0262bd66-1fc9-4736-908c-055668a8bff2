<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>./app/Console</directory>
            <directory>./app/Providers</directory>
            <directory>./app/Models</directory>
            <directory>./app/Exceptions</directory>
            <directory>./app/Repositories</directory>
            <directory>./app/Events</directory>
            <directory>./app/Classes/Services/Retrieval/Criteria</directory>
            <directory>./app/Classes/Services/Criteria</directory>
            <directory>./app/Classes/ValueObjects</directory>
            <directory>./app/Http/Controllers</directory>
            <directory>./app/Http/Requests</directory>
            <file>./app/Modules/AiModels/GeminiAccessTokenProvider.php</file>
        </exclude>
    </source>
    <coverage>
        <report>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <!-- <env name="DB_CONNECTION" value="sqlite"/> -->
        <!-- <env name="DB_DATABASE" value=":memory:"/> -->
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
