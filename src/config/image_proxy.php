<?php

declare(strict_types=1);

return [
    'host'            => env('IMAGE_PROXY_HOST', 'https://imgproxy.newswav.com'),
    'secret_key'      => env('IMAGE_PROXY_SECRET_KEY', 'secretKey'),
    'square_size'     => env('IMAGE_PROXY_SQUARE_SIZE', 1000),
    'wide_width'      => env('IMAGE_WIDE_WIDTH_SIZE', 1000),
    'wide_height'     => env('IMAGE_WIDE_HEIGHT_SIZE', 400),
    'regular_width'   => env('IMAGE_PROXY_REGULAR_WIDTH', 1000),
    'regular_height'  => env('IMAGE_PROXY_REGULAR_HEIGHT', 0),
    'regular_quality' => env('IMAGE_PROXY_REGULAR_QUALITY', 50),
    'image_quality'   => env('IMAGE_PROXY_QUALITY', 50),
];
