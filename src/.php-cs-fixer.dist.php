<?php

use <PERSON>p<PERSON><PERSON><PERSON><PERSON>\Finder;
use PhpCs<PERSON>ixer\Config;

$config = (new Config())->setRiskyAllowed(true)
    ->setRules(
        [
            '@PSR1'                   => true,
            '@PSR2'                   => true,
            '@Symfony'                => true,
            '@DoctrineAnnotation'     => true,
            'align_multiline_comment' => ['comment_type' => 'phpdocs_only'],
            'array_indentation'       => true,
            'array_syntax'            => ['syntax' => 'short'],
            'binary_operator_spaces'  => [
                'operators' => [
                    '=>' => 'align_single_space',
                    '='  => 'align_single_space',
                ],
            ],
            'blank_line_before_statement' => [
                'statements' => [
                    'break',
                    'continue',
                    'declare',
                    'return',
                    'throw',
                    'try',
                ],
            ],
            'braces_position' => [
                'allow_single_line_anonymous_functions'     => false,
                'allow_single_line_empty_anonymous_classes' => false,
                'anonymous_classes_opening_brace'           => 'same_line',
                'anonymous_functions_opening_brace'         => 'same_line',
                'classes_opening_brace'                     => 'same_line',
                'control_structures_opening_brace'          => 'same_line',
                'functions_opening_brace'                   => 'same_line',
            ],
            'class_attributes_separation' => [
                'elements' => [
                    'const'    => 'one',
                    'method'   => 'one',
                    'property' => 'one',
                ],
            ],
            'combine_consecutive_issets'              => true,
            'combine_consecutive_unsets'              => true,
            'compact_nullable_type_declaration'       => true,
            'control_structure_braces'                => true,
            'control_structure_continuation_position' => true,
            'concat_space'                            => ['spacing' => 'one'],
            'declare_parentheses'                     => true,
            'explicit_indirect_variable'              => true,
            'explicit_string_variable'                => true,
            'line_ending'                             => false,
            'general_phpdoc_annotation_remove'        => ['annotations' => ['package']],
            'list_syntax'                             => ['syntax' => 'short'],
            'increment_style'                         => ['style' => 'post'],
            'no_alternative_syntax'                   => true,
            'no_blank_lines_after_class_opening'      => true,
            'no_blank_lines_after_phpdoc'             => true,
            'no_empty_phpdoc'                         => true,
            'no_extra_blank_lines'                    => true,
            'no_multiple_statements_per_line'         => true,
            'single_space_around_construct'           => true,
            'statement_indentation'                   => true,
            'blank_line_after_namespace'              => true,
            'phpdoc_trim'                             => true,
            'no_empty_comment'                        => true,
            'no_empty_statement'                      => true,
            'no_closing_tag'                          => true,
            'method_chaining_indentation'             => true,
            'no_useless_return'                       => true,
            'no_useless_else'                         => true,
            'not_operator_with_space'                 => true,
            'phpdoc_order'                            => true,
            'phpdoc_types_order'                      => ['null_adjustment' => 'always_last'],
            'phpdoc_add_missing_param_annotation'     => ['only_untyped' => true],
            'ternary_to_null_coalescing'              => true,
            'strict_param'                            => true,
            // risky elements
            'void_return'        => true,
            'comment_to_phpdoc'  => true,
            'no_alias_functions' => true,
            'is_null'            => true,
            'strict_comparison'  => true,
            // symfony overrides
            'nullable_type_declaration_for_default_null_value' => true,
            'no_multiline_whitespace_around_double_arrow'      => true,
            'yoda_style'                                       => [
                'equal'            => false,
                'identical'        => false,
                'less_and_greater' => false,
            ],
            'phpdoc_annotation_without_dot'     => false,
            'fully_qualified_strict_types'      => true,
            'php_unit_fqcn_annotation'          => true,
            'declare_strict_types'              => true,
            'blank_lines_before_namespace'      => ['min_line_breaks' => 2, 'max_line_breaks' => 2],
            'single_trait_insert_per_statement' => false,
            'ordered_class_elements'            => true,
            'no_superfluous_phpdoc_tags'        => [
                'allow_mixed'       => true,
                'remove_inheritdoc' => true,
            ],
            'phpdoc_to_comment'       => false,
            'types_spaces'            => ['space' => 'single'],
            'global_namespace_import' => [
                'import_classes'   => true,
                'import_constants' => null,
                'import_functions' => null,
            ],
        ]
    );
$finder = Finder::create()->in(['app', 'config', 'database', 'routes', 'tests']);

$config->setFinder($finder);

return $config;
