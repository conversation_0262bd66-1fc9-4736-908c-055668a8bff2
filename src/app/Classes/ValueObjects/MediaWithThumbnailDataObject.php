<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Models\Media;
use App\Models\Thumbnail;

class MediaWithThumbnailDataObject {
    public function __construct(
        private Media $media,
        private ?Thumbnail $thumbnail = null,
    ) {
    }

    public function getMedia(): Media {
        return $this->media;
    }

    public function getThumbnail(): ?Thumbnail {
        return $this->thumbnail;
    }

    public function getMediaId(): int {
        return $this->media->id;
    }

    public function getThumbnailURL(): string {
        return $this->thumbnail->wideUrl ?? '';
    }

    public function getWideImageWidth(): int {
        return $this->thumbnail->wideImageWidth ?? 0;
    }

    public function getWideImageHeight(): int {
        return $this->thumbnail->wideImageHeight ?? 0;
    }
}
