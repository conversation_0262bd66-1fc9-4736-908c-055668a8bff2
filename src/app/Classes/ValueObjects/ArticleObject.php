<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class ArticleObject {
    public function __construct(
        private int $articleID,
        private string $uniqueID,
        private int $channelID,
        private string $title,
        private string $description,
        private string $html,
        private string $author,
        private string $publishedDate,
        private string $modifiedDate,
        private string $url,
        private string $canonicalURL,
        private string $media,
        private string $permalink,
        private string $contentMd5,
        private string $relatedArticle = '',
    ) {
    }

    public function getArticleId(): int {
        return $this->articleID;
    }

    public function getUniqueId(): string {
        return $this->uniqueID;
    }

    public function getChannelId(): int {
        return $this->channelID;
    }

    public function getTitle(): string {
        return $this->title;
    }

    public function getDescription(): string {
        return $this->description;
    }

    public function getHtml(): string {
        return $this->html;
    }

    public function getAuthor(): string {
        return $this->author;
    }

    public function getPublishedDate(): string {
        return $this->publishedDate;
    }

    public function getModifiedDate(): string {
        return $this->modifiedDate;
    }

    public function getUrl(): string {
        return $this->url;
    }

    public function getCanonicalURL(): string {
        return $this->canonicalURL;
    }

    public function getMedia(): string {
        return $this->media;
    }

    public function getPermalink(): string {
        return $this->permalink;
    }

    public function getContentMd5(): string {
        return $this->contentMd5;
    }

    public function getRelatedArticle(): string {
        return $this->relatedArticle;
    }

    public function setRelatedArticle(string $relatedArticle): void {
        $this->relatedArticle = $relatedArticle;
    }
}
