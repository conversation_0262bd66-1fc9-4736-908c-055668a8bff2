<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class ParsedArticleObject {
    public function __construct(
        private int $articleID,
        private string $title,
        private string $description,
        private string $fullContent,
        private string $author,
        private string $publishedDate,
        private string $modifiedDate,
        private string $url,
        private string $canonicalURL,
        private array $coverImage,
        private array $media,
        private string $contentMd5,
    ) {
    }

    public function getArticleId(): int {
        return $this->articleID;
    }

    public function getTitle(): string {
        return $this->title;
    }

    public function getDescription(): string {
        return $this->description;
    }

    public function getFullContent(): string {
        return $this->fullContent;
    }

    public function getAuthor(): string {
        return $this->author;
    }

    public function setPublishedDate(string $publishedDate): void {
        $this->publishedDate = $publishedDate;
    }

    public function getPublishedDate(): string {
        return $this->publishedDate;
    }

    public function getModifiedDate(): string {
        return $this->modifiedDate;
    }

    public function getUrl(): string {
        return $this->url;
    }

    public function getCanonicalURL(): string {
        return $this->canonicalURL;
    }

    public function getCoverImage(): array {
        return $this->coverImage;
    }

    public function getCoverImageUrl(): string {
        return $this->coverImage['url'] ?? '';
    }

    public function getCoverImageCaption(): string {
        return $this->coverImage['caption'] ?? '';
    }

    public function getMedia(): array {
        return $this->media;
    }

    public function getContentMd5(): string {
        return $this->contentMd5;
    }
}
