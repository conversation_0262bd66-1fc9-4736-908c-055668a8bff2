<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class CrawlerSettingObject {
    public function __construct(
        private int $publisherId,
        private int $channelId,
        private string $host,
        private bool $enabled,
        private int $partner,
        private bool $needRandom,
        private int $frequency,
        private string $type,
        private string $crawlType,
        private ?string $customFetcher,
        private string $worker,
        private ?string $customUserAgent,
        private bool $useSmartCrawler,
        private bool $useHeadlessBrowser,
        private ?string $customPrompt,
        private bool $toPuppeteer,
    ) {
    }

    public function getPublisherId(): int {
        return $this->publisherId;
    }

    public function getChannelId(): int {
        return $this->channelId;
    }

    public function getHost(): string {
        return $this->host;
    }

    public function isEnabled(): bool {
        return $this->enabled;
    }

    public function isPartner(): int {
        return $this->partner;
    }

    public function needRandom(): bool {
        return $this->needRandom;
    }

    public function getFrequency(): int {
        return $this->frequency;
    }

    public function getType(): string {
        return $this->type;
    }

    public function getCrawlType(): string {
        return $this->crawlType;
    }

    public function getCustomFetcher(): ?string {
        return $this->customFetcher;
    }

    public function getWorker(): string {
        return $this->worker;
    }

    public function getCustomUserAgent(): ?string {
        return $this->customUserAgent;
    }

    public function useSmartCrawler(): bool {
        return $this->useSmartCrawler;
    }

    public function useHeadlessBrowser(): bool {
        return $this->useHeadlessBrowser;
    }

    public function getCustomPrompt(): ?string {
        return $this->customPrompt;
    }

    public function toPuppeteer(): bool {
        return $this->toPuppeteer;
    }
}
