<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class ArticleProcessingDataObject {
    public function __construct(
        private int $publisherId,
        private int $channelId,
        private string $customPrompt,
        private bool $useBypassCloudflare,
        private bool $useHeadlessBrowser,
        private ?string $articleDataRssItem,
        private ?string $articleDataHtmlLink,
        private bool $useAiParsing,
    ) {
    }

    public function getPublisherId(): int {
        return $this->publisherId;
    }

    public function getChannelId(): int {
        return $this->channelId;
    }

    public function getArticleDataRssItem(): ?string {
        return $this->articleDataRssItem;
    }

    public function getArticleDataHtmlLink(): ?string {
        return $this->articleDataHtmlLink;
    }

    public function getCustomPrompt(): string {
        return $this->customPrompt;
    }

    public function useBypassCloudflare(): bool {
        return $this->useBypassCloudflare;
    }

    public function useHeadlessBrowser(): bool {
        return $this->useHeadlessBrowser;
    }

    public function useAiParsing(): bool {
        return $this->useAiParsing;
    }
}
