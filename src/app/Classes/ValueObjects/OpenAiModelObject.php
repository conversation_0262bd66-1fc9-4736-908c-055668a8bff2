<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Classes\Interfaces\AiModel;
use App\Enums\AiModels;

class OpenAiModelObject implements AiModel {
    public function __construct(
        protected string $apikey,
        protected string $baseUrl,
        protected string $userPrompt,
        protected string $systemPrompt,
        private AiModels $model = AiModels::GPT_4_0_o_MINI,
        protected float $temperature = 0.7,
    ) {
    }

    public function getModelParameters(): array {
        return [
            'model'    => $this->model->value,
            'messages' => [
                ['role' => 'system', 'content' => $this->systemPrompt],
                ['role' => 'user', 'content' => $this->userPrompt],
            ],
            'temperature' => $this->temperature,
        ];
    }

    public function getModelName(): string {
        return $this->model->value;
    }

    public function getModelUrl(): string {
        return $this->baseUrl;
    }

    public function getApiKey(): string {
        return $this->apikey;
    }
}
