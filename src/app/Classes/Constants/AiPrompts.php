<?php

declare(strict_types=1);

namespace App\Classes\Constants;

class AiPrompts {
    final public const EXTRACT_ARTICLE_LINKS_FROM_HTML_SYSTEM_PROMPT = <<<PROMPT
    You are an expert web scraper. Your task is to extract all fully qualified URLs pointing to individual news articles from the main content section of a given HTML page. 
    Use the provided base URL to resolve relative links. Exclude URLs from any content outside the main article listing, such as sidebars, footers, navigation, category/tag pages, pagination, advertisements, related/suggested articles, and social media. Return the results as an array of absolute article URLs, ensuring each URL directly links to a distinct news article page within the main content's primary listing, regardless of the specific website structure or page layout.

    Return the output as a **valid array of unique absolute URLs**. 
    Do **not** include any explanation, markdown, or formatting — only the array.
    PROMPT;

    final public const EXTRACT_ARTICLE_LINKS_FROM_HTML_USER_PROMPT = <<<PROMPT
    Given the following raw HTML, extract and return only an array of absolute URLs to individual news articles. Use the base URL to resolve any relative paths. Do not return any explanation or extra formatting. Do not include links that lead to pagination, category listings, or navigation pages (e.g., "next", "previous", "page=2").

    :raw_html
    PROMPT;

    final public const EXTRACT_ARTICLE_METADATA_SYSTEM_PROMPT = <<<PROMPT
    You are an intelligent content parser. Extract meaningful content from raw HTML or plain text. Return a well-structured JSON object with real values, not a schema. Be precise and follow the field structure exactly. Ensure all content of each json field is properly escaped for JSON.
    PROMPT;

    final public const EXTRACT_ARTICLE_METADATA_FROM_HTML_CONTENT_USER_PROMPT = <<<PROMPT
    You are an expert content parser.

    Extract the following structured data from the given raw article content. Ensure all values are accurate and complete:

    - **Article ID** (unique identifier, extracted from the source URL if no clear article ID is provided, must be integer without any characters)
    - **Original link** (source URL)
    - **Title** (clear and concise)
    - **Description** (summary or excerpt)
    - **Author** (person or organization)
    - **Publication date** (as a date-time string in YYYY-MM-DD HH:mm:ss format)
    - **Last modified date** (as a date-time string in YYYY-MM-DD HH:mm:ss format, otherwise same as publication date)
    - **Full article content**:
        - Extract *only* the core article as valid, semantic HTML.
        - *Remove ALL attributes* (class, id, style, data-*, aria-*) from *every* HTML tag. The output *must* be clean, attribute-free HTML.
        - Remove unnecessary whitespace (tabs, newlines, extra spaces). Output should be compact.
        - Omit layout elements (header, footer, nav, ads, pop-ups, etc.).
        - Omit any recommended or suggested content, such as “More articles”, “Now Read”, “You might like”, “Recommended for you”, and similar sections.
        - Omit "Return to..." links and routing buttons.
        - Keep semantic tags (`<h1>`-`<h6>`, `<p>`, `<ul>`, `<ol>`, `<li>`, `<blockquote>`, `<code>`, `<pre>`, `<img>`, `<video>`, `<iframe>`, `<a>`).
        - Retain code block formatting and complete media embed structures.
        - The output MUST include images, videos, and embeds as they appear in the article body.
    - **Media** (always return empty array [])
    - **Cover image** (URL and optional caption)

    Retain all styling for code blocks. Use proper embed code and required scripts for social media and multimedia.
    
    Here is the content to analyze:
    :raw_content

    PROMPT;

    final public const EXTRACT_ARTICLE_METADATA_ROM_RSS_ITEM_USER_PROMPT = <<<PROMPT
    You are an expert content parser.

    Extract the following structured data from the given raw article content. Ensure all values are accurate and complete:

    - **Article ID** (unique identifier, extracted from the source URL if no clear article ID is provided, must be integer without any characters)
    - **Original link** (source URL)
    - **Title** (clear and concise)
    - **Description** (summary or excerpt)
    - **Author** (person or organization)
    - **Publication date** (as a date-time string in YYYY-MM-DD HH:mm:ss format)
    - **Last modified date** (as a date-time string in YYYY-MM-DD HH:mm:ss format, otherwise same as publication date)
    - **Media** (array of objects, each containing a URL and optional caption for images or videos. Can be empty array [] if there is no media in the given raw content, but must present in the output)
    - **Cover image** (URL and optional caption)

    Use proper embed code and required scripts for social media and multimedia.
    
    Here is the content to analyze:
    :raw_content

    PROMPT;

    final public const EXTRACT_ARTICLE_METADATA_FROM_HTML_CONTENT_JSON_SCHEMA = <<<PROMPT
    Return the extracted content as a valid JSON object with actual values (not a schema). Example format:

    {
    "article_id": {
        "type": "number"
    },
    "link": {
        "type": "string"
    },
    "title": {
        "type": "string"
    },
    "description": {
        "type": "string"
    },
    "full_content": {
        "type": "string"
    },
    "cover_image_url": {
        "type": "object",
        "properties": {
        "url": {
            "type": "string"
        },
        "caption": {
            "type": "string",
            "nullable": true
        }
        },
        "required": ["url"]
    },
    "media": {
        "type": "array",
    },
    "author": {
        "type": "string"
    },
    "published_date": {
        "type": "string"
    },
    "modified_date": {
        "type": "string"
    }
    }

    IMPORTANT: Return only a single valid JSON object with **real extracted values**. The output **must be safe for JSON parsing** — this means escaping all special characters such as `"` (quote), `\` (backslash), newlines and HTML content correctly.
    Ensure that:
    - All string values are **valid JSON strings** (no raw unescaped HTML or JavaScript).
    - No markdown, comments, or extra text outside the JSON.
    - `full_content` must be a valid escaped string within JSON — e.g. `"<p>This is valid</p>"` not raw HTML.

    PROMPT;

    final public const EXTRACT_ARTICLE_METADATA_ROM_RSS_ITEM_JSON_SCHEMA = <<<PROMPT
    Return the extracted content in this exact valid JSON format, with real values based on the input in the format below:

    {
    "article_id": {
        "type": "number"
    },
    "link": {
        "type": "string"
    },
    "title": {
        "type": "string"
    },
    "description": {
        "type": "string"
    },
    "cover_image_url": {
        "type": "object",
        "properties": {
        "url": {
            "type": "string"
        },
        "caption": {
            "type": "string",
            "nullable": true
        }
        },
        "required": ["url"]
    },
    "media": {
        "type": "array",
        "items": {
        "type": "object",
        "properties": {
            "url": {
            "type": "string"
            },
            "caption": {
            "type": "string",
            "nullable": true
            }
        },
        "required": ["url"]
        }
    },
    "author": {
        "type": "string"
    },
    "published_date": {
        "type": "string"
    },
    "modified_date": {
        "type": "string"
    }
    }
    
    IMPORTANT: Return only a single valid JSON object with **real extracted values**. The output **must be safe for JSON parsing** — this means escaping all special characters such as `"` (quote), `\` (backslash), newlines and HTML content correctly.
    Ensure that:
    - All string values are **valid JSON strings** (no raw unescaped HTML or JavaScript).
    - No markdown, comments, or extra text outside the JSON.

    PROMPT;
}
