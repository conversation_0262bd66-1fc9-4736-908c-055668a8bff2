<?php

declare(strict_types=1);

namespace App\Classes\Services\Criteria;

use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Builder;

class HasProxyImageForPublisher implements Criteriable {
    public function __construct(
        private int $publisherId,
        private int $channelId,
    ) {
    }

    public function apply(Builder $builder): Builder {
        return $builder
            ->select('has_proxy_image')
            ->with('crawlerSetting')
            ->whereHas('crawlerSetting', function (Builder $query): void {
                $query->where('publisher_id', $this->publisherId)
                    ->where('channel_id', $this->channelId)
                    ->where('enabled', true)
                    ->where('use_smart_crawler', true);
            });
    }

    public function getApplicableTo(): ?string {
        return PublisherEndpoint::class;
    }
}
