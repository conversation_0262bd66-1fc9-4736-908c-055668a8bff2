<?php

declare(strict_types=1);

namespace App\Classes\Services\Criteria;

use Illuminate\Database\Eloquent\Builder;

interface Criteriable {
    /**
     * Apply a set of criteria to a builder query.
     */
    public function apply(Builder $builder): Builder;

    /**
     * Returns the class of the model that this criteria is applicable to.
     */
    public function getApplicableTo(): ?string;
}
