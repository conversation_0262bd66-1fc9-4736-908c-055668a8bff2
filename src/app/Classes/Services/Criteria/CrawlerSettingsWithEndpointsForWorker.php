<?php

declare(strict_types=1);

namespace App\Classes\Services\Criteria;

use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Builder;

class CrawlerSettingsWithEndpointsForWorker implements Criteriable {
    public function __construct(
        private string $workerId,
    ) {
    }

    public function apply(Builder $builder): Builder {
        return $builder
            ->with('crawlerSetting')
            ->whereHas('crawlerSetting', function (Builder $query): void {
                $query->where('worker', $this->workerId)
                    ->where('enabled', true)
                    ->where('use_smart_crawler', true);
            })
            ->where(function (Builder $query): void {
                $query->whereNull('last_checked')
                    ->orWhereRaw('
                        UNIX_TIMESTAMP() >= last_checked + (
                            SELECT frequency * 60 
                            FROM publisher_crawler_settings 
                            WHERE publisher_crawler_settings.id = publisher_endpoints.crawler_setting_id
                        )
                    ');
            });
    }

    public function getApplicableTo(): ?string {
        return PublisherEndpoint::class;
    }
}
