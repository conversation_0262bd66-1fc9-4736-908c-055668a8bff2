<?php

declare(strict_types=1);

namespace App\Classes\Services\Criteria;

use App\Models\Prediction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ArticleContentWithPredictionData implements Criteriable {
    public function __construct(private string $uniqueId) {
    }

    public function apply(Builder $builder): Builder {
        return $builder
            ->where('unique_id', $this->uniqueId)
            ->join('article as a', 'predictions.unique_id', '=', 'a.uniqueID')
            ->join('channels as c', 'c.id', '=', 'a.channelID')
            ->join('publishers as pub', 'pub.id', '=', 'predictions.publisher_id')
            ->leftJoin('thumbnail as t', function ($join): void {
                $join->on('t.mediaOwnerId', '=', DB::raw("SUBSTRING_INDEX(a.media, ',', 1)"));
            })
            ->leftJoin('topics as tp', function ($join): void {
                $join->on('tp.id', '=', 'predictions.topic')
                    ->where('predictions.topic', '!=', -1);
            })
            ->select(
                'predictions.unique_id',
                'a.title',
                'a.description',
                'a.publishedDate',
                'predictions.publisher_id',
                'predictions.language',
                'a.permalink',
                'predictions.topic',
                't.wideUrl as thumbnailURL',
                't.wideImageWidth',
                't.wideImageHeight',
                'c.reader_view_only as native',
                'a.media',
                'pub.name as pubName',
                'pub.project',
                'pub.logo_url as imageUrl',
                'pub.website_url as pubUrl',
                'pub.enabled as pubEnabled',
                'pub.verified',
                'pub.permalink as pub_permalink',
                'a.canonicalURL as originalUrl',
                'tp.name_en as topic_en',
                'tp.name_zh as topic_zh',
                'tp.name_ms as topic_ms'
            )
            ->where('predictions.language', '!=', 'DEL');
    }

    public function getApplicableTo(): ?string {
        return Prediction::class;
    }
}
