<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\ServerParameters;
use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\APIException;
use Exception;
use GuzzleHttp\Client;
use Guz<PERSON>Http\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class GuzzleReadService {
    public function __construct(private Client $client) {
    }

    /**
     * @throws APIException
     *
     * @return RawContentObject|null raw html or rss feed, null if timeout occurs
     */
    public function getRawHtmlOrRss(string $endpointUrl, ?string $customUserAgent = null): ?RawContentObject {
        try {
            $response = $this->makeRequest($endpointUrl, $customUserAgent);

            if ($response === null) {
                return null;
            }

            $content = $response->getBody()->getContents();
            if (empty($content) === true) {
                throw new APIException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Guzzle Read Service returned empty response for URL: ' . $endpointUrl);
            }

            return new RawContentObject($content);
        } catch (Exception $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    private function makeRequest(string $endpointUrl, ?string $customUserAgent = null): ?ResponseInterface {
        try {
            $response =  $this->client->request(ServerParameters::HTTP_METHOD_GET, $endpointUrl, [
                'headers' => [
                    'User-Agent' => $customUserAgent ?? 'newswav-fetcher',
                ],
                'timeout' => 30,
            ]);

            return $response;
        } catch (ConnectException $exception) {
            Log::info("GuzzleReadService timeout for URL: {$endpointUrl}");

            return null;
        } catch (ClientException $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();

            // Handle common RSS feed errors gracefully
            if (in_array($statusCode, [404, 403, 410], true)) {
                Log::warning("RSS feed client error for URL: {$endpointUrl} - Status: {$statusCode} - {$exception->getMessage()}");
                return null;
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (ServerException $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();

            // Handle server errors gracefully (503, 502, 500, etc.)
            if (in_array($statusCode, [500, 502, 503, 504], true)) {
                Log::warning("RSS feed server error for URL: {$endpointUrl} - Status: {$statusCode} - {$exception->getMessage()}");
                return null;
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
