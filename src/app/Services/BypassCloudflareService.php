<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\ServerParameters;
use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\APIException;
use Exception;
use GuzzleHttp\Client;
use Guz<PERSON>Http\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class BypassCloudflareService {
    public function __construct(private Client $client) {
    }

    /**
     * @throws APIException
     *
     * @return RawContentObject|null raw html or rss feed, null if timeout occurs
     */
    public function getRawHtmlOrRss(string $endpointUrl, ?string $customUserAgent = null, ?array $customCookies = null): ?RawContentObject {
        try {
            $response = $this->makeRequest(array_filter([
                'url'        => $endpointUrl,
                'user_agent' => $customUserAgent,
                'cookies'    => $customCookies,
            ]));

            if ($response === null) {
                return null;
            }

            $content = $response->getBody()->getContents();
            if (empty($content)) {
                throw new APIException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Bypass Cloudflare Service returned empty response for URL: ' . $endpointUrl);
            }

            return new RawContentObject($content);
        } catch (Exception $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    private function makeRequest(array $requestData): ?ResponseInterface {
        try {
            $bypassServiceUrl = config('bypass_cloudflare.service_url');
            $timeout          = config('bypass_cloudflare.timeout', 30);

            $response =  $this->client->request(ServerParameters::HTTP_METHOD_POST, $bypassServiceUrl, [
                'headers' => [
                    'Content-Type'  => 'application/json',
                ],
                'json'    => $requestData,
                'timeout' => $timeout,
            ]);

            return $response;
        } catch (ConnectException $exception) {
            $url = $requestData['url'] ?? 'unknown';
            Log::info("BypassCloudflareService timeout for URL: {$url} after {$timeout}s");

            return null;
        } catch (ClientException $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();
            $url = $requestData['url'] ?? 'unknown';

            // Handle common RSS feed errors gracefully
            if (in_array($statusCode, [404, 403, 410], true)) {
                Log::warning("BypassCloudflareService client error for URL: {$url} - Status: {$statusCode} - {$exception->getMessage()}");
                return null;
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (ServerException $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();
            $url = $requestData['url'] ?? 'unknown';

            // Handle server errors gracefully (503, 502, 500, etc.)
            if (in_array($statusCode, [500, 502, 503, 504], true)) {
                Log::warning("BypassCloudflareService server error for URL: {$url} - Status: {$statusCode} - {$exception->getMessage()}");
                return null;
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
