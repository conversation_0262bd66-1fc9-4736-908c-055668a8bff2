<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\UnauthorisedException;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class Authenticate {
    /**
     * @throws UnauthorisedException
     */
    public function handle(Request $request, Closure $next): JsonResponse {
        if (in_array(app()->env, ['production', 'development'], true) === false) {
            return $next($request);
        }

        $authorization = $request->header('Authorization');

        if ($authorization === null || str_starts_with($authorization, 'Bearer ') === false) {
            throw new UnauthorisedException();
        }

        $jwt = substr($authorization, 7);

        if ($jwt === config('auth.jwt.secret')) {
            return $next($request);
        }

        throw new UnauthorisedException();
    }
}
