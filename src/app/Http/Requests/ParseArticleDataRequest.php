<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ParseArticleDataRequest extends FormRequest {
    public function rules(): array {
        return [
            'publisher_id'           => 'required|integer',
            'channel_id'             => 'required|integer',
            'article_data_rss_item'  => 'required_without:article_data_html_link|string',
            'article_data_html_link' => 'required_without:article_data_rss_item|string',
            'custom_prompt'          => 'nullable|string',
            'use_headless_browser'   => 'nullable|boolean',
            'use_ai_parsing'         => 'required|boolean',
        ];
    }
}
