<?php

declare(strict_types=1);

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel {
    final public const MIDDLEWARE_AUTH = 'newswav.auth';

    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's middleware aliases.
     *
     * Aliases may be used instead of class names to conveniently assign middleware to routes and groups.
     *
     * @var array<string, class-string|string>
     */
    protected $middlewareAliases = [
        self::MIDDLEWARE_AUTH => Middleware\Authenticate::class,
        'auth.basic'          => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session'        => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers'       => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can'                 => \Illuminate\Auth\Middleware\Authorize::class,
        'password.confirm'    => \Illuminate\Auth\Middleware\RequirePassword::class,
        'precognitive'        => \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
        'signed'              => Middleware\ValidateSignature::class,
        'throttle'            => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified'            => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
    ];
}
