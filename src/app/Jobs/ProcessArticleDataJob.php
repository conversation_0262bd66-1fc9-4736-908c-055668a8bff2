<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleProcessingDataObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessArticleDataJob implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 2;

    public int $backoff = 10;

    public function __construct(public ArticleProcessingDataObject $articleProcessingData) {
    }

    public function handle(
        ParseArticleDataLogic $parseArticleDataLogic,
        PopulateArticleDataIntoDatabaseLogic $populateArticleDataIntoDatabaseLogic,
    ): void {
        Log::info(
            sprintf('Processing queued article data for publisher %s and channel %s',
                $this->articleProcessingData->getPublisherId(),
                $this->articleProcessingData->getChannelId()
            )
        );

        // Parse article data
        $articleParsed = $parseArticleDataLogic->execute($this->articleProcessingData);

        if (empty($articleParsed) === true) {
            Log::info('No article data parsed, skipping database population');

            return;
        }

        Log::info('Populating article data into database from queue for publisher ' . $this->articleProcessingData->getPublisherId());

        // Populate article data into database
        $populateArticleDataIntoDatabaseLogic->execute(new ParsedArticleObject(
            $articleParsed[Parser::ARTICLE_ID],
            $articleParsed[Parser::TITLE],
            $articleParsed[Parser::DESCRIPTION],
            $articleParsed[Parser::FULL_CONTENT],
            $articleParsed[Parser::AUTHOR],
            $articleParsed[Parser::PUBLISHED_DATE],
            $articleParsed[Parser::MODIFIED_DATE],
            $articleParsed[Parser::LINK],
            $articleParsed[Parser::CANONICAL_URL],
            $articleParsed[Parser::COVER_IMAGE_URL],
            $articleParsed[Parser::MEDIA],
            $articleParsed[Parser::CONTENT_MD5],
        ), $this->articleProcessingData->getPublisherId(), $this->articleProcessingData->getChannelId());
    }
}
