<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Classes\Constants\Parser;
use App\Classes\Constants\ServerParameters;
use Exception;
use Illuminate\Support\Str;
use Throwable;

class MediaHelper {
    public function isImageUrl(string $url): bool {
        $path      = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        return in_array(strtolower($extension), Parser::IMAGE_EXTENSIONS, true);
    }

    public function isVideoUrl(string $url): bool {
        $path      = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        return in_array(strtolower($extension), Parser::VIDEO_EXTENSIONS, true);
    }

    public function getImageSize(string $imageUrl): array {
        try {
            $imageData = $this->fetchImageData($imageUrl);

            if ($imageData === false || empty($imageData)) {
                return ['width' => 0, 'height' => 0];
            }

            $imageSize = $this->getImageSizeFromData($imageData);
            if ($imageSize && isset($imageSize[0], $imageSize[1])) {
                return [
                    'width'  => $imageSize[0],
                    'height' => $imageSize[1],
                ];
            }

            return ['width' => 0, 'height' => 0];
        } catch (Throwable $e) {
            return ['width' => 0, 'height' => 0];
        }
    }

    public function isMediaUrlValid(string $link): bool {
        try {
            if (Str::startsWith(strtolower($link), 'http') === false) {
                return false;
            }

            $httpCode = $this->checkUrlResponseCode($link);

            return $httpCode === ServerParameters::HTTP_STATUS_OK;
        } catch (Exception $e) {
            return false;
        }
    }

    protected function fetchImageData(string $imageUrl): bool | string {
        $ch = null;

        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL            => $imageUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CONNECTTIMEOUT => 5,
                CURLOPT_TIMEOUT        => 15,
                CURLOPT_HTTPHEADER     => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
                ],
                CURLOPT_FAILONERROR => true,
            ]);

            return curl_exec($ch);
        } finally {
            curl_close($ch);
        }
    }

    protected function getImageSizeFromData(string $imageData): array | false {
        return @getimagesizefromstring($imageData);
    }

    protected function checkUrlResponseCode(string $url): int {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        ]);
        curl_exec($ch);

        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $http_code;
    }
}
