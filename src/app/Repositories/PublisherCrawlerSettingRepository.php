<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Classes\ValueObjects\CrawlerSettingObject;
use App\Models\PublisherCrawlerSetting;

/**
 * @extends BaseRepository<PublisherCrawlerSetting>
 */
class PublisherCrawlerSettingRepository extends BaseRepository {
    public function __construct(PublisherCrawlerSetting $model) {
        parent::__construct($model);
    }

    public function createFromObject(CrawlerSettingObject $crawlerSettingObject): PublisherCrawlerSetting {
        return $this->create([
            'publisher_id'         => $crawlerSettingObject->getPublisherId(),
            'channel_id'           => $crawlerSettingObject->getChannelId(),
            'host'                 => $crawlerSettingObject->getHost(),
            'enabled'              => $crawlerSettingObject->isEnabled(),
            'partner'              => $crawlerSettingObject->isPartner(),
            'need_random'          => $crawlerSettingObject->needRandom(),
            'frequency'            => $crawlerSettingObject->getFrequency(),
            'type'                 => $crawlerSettingObject->getType(),
            'crawl_type'           => $crawlerSettingObject->getCrawlType(),
            'custom_fetcher'       => $crawlerSettingObject->getCustomFetcher(),
            'worker'               => $crawlerSettingObject->getWorker(),
            'custom_user_agent'    => $crawlerSettingObject->getCustomUserAgent(),
            'use_smart_crawler'    => $crawlerSettingObject->useSmartCrawler(),
            'use_headless_browser' => $crawlerSettingObject->useHeadlessBrowser(),
            'custom_prompt'        => $crawlerSettingObject->getCustomPrompt(),
            'to_puppeteer'         => $crawlerSettingObject->toPuppeteer(),
        ]);
    }

    public function updateFromObject(CrawlerSettingObject $crawlerSettingObject, int $crawlerSettingId): bool {
        return $this->update('id', $crawlerSettingId, [
            'host'                 => $crawlerSettingObject->getHost(),
            'enabled'              => $crawlerSettingObject->isEnabled(),
            'partner'              => $crawlerSettingObject->isPartner(),
            'need_random'          => $crawlerSettingObject->needRandom(),
            'frequency'            => $crawlerSettingObject->getFrequency(),
            'type'                 => $crawlerSettingObject->getType(),
            'crawl_type'           => $crawlerSettingObject->getCrawlType(),
            'custom_fetcher'       => $crawlerSettingObject->getCustomFetcher(),
            'worker'               => $crawlerSettingObject->getWorker(),
            'custom_user_agent'    => $crawlerSettingObject->getCustomUserAgent(),
            'use_smart_crawler'    => $crawlerSettingObject->useSmartCrawler(),
            'use_headless_browser' => $crawlerSettingObject->useHeadlessBrowser(),
            'custom_prompt'        => $crawlerSettingObject->getCustomPrompt(),
            'to_puppeteer'         => $crawlerSettingObject->toPuppeteer(),
        ]);
    }
}
