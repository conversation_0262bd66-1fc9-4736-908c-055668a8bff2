<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Classes\Services\Criteria\ArticleContentWithPredictionData;
use App\Models\Prediction;

/**
 * @extends BaseRepository<Prediction>
 */
class PredictionsRepository extends BaseRepository {
    public function __construct(Prediction $model) {
        parent::__construct($model);
    }

    public function getArticleDataWithPredictionData(string $uniqueId): ?Prediction {
        return $this->getBuilderBasedOnCriteria(
            new ArticleContentWithPredictionData($uniqueId)
        )->first();
    }
}
