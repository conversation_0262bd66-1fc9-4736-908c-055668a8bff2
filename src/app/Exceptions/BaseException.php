<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;

class BaseException extends Exception {
    protected int $statusCode;

    protected string $errorMessageCode;

    public function __construct(int $statusCode, string $errorMessageCode) {
        parent::__construct($errorMessageCode, $statusCode);

        $this->statusCode       = $statusCode;
        $this->errorMessageCode = $errorMessageCode;
    }

    public function getStatusCode(): int {
        return $this->statusCode;
    }

    public function getErrorMessageCode(): string {
        return $this->errorMessageCode;
    }

    public function getLocalisedTitle(): string {
        $key   = sprintf('exception.%s.title', $this->getErrorMessageCode());
        $title = __($key, [], 'en');

        // Return default title if translation not found
        return $key !== $title ?
            $title :
            __('exception.default_title', [], 'en');
    }

    public function getLocalisedMessage(): string {
        $key     = sprintf('exception.%s.message', $this->getErrorMessageCode());
        $message = __($key, [], 'en');

        // Return default title if translation not found
        return $key !== $message ? $message : $this->getErrorMessageCode();
    }
}
