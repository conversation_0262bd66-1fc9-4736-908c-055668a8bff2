<?php

declare(strict_types=1);

namespace App\Exceptions;

class InvalidAiResponseException extends BaseException {
    public function __construct(int $errorCode, string $jsonErrorMessage, string $aiResponse, string $replacedInput) {
        $errorMessage = sprintf("Invalid JSON received from AI.\nError: %s\n\n--- Raw Input ---\n %s\n\n--- Cleaned Input ---\n %s",
            $jsonErrorMessage,
            $aiResponse,
            $replacedInput
        );
        parent::__construct($errorCode, $errorMessage);
    }
}
