<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\FetcherCommandException;
use App\Modules\Fetcher\Logics\FetchAndProcessEndpointForWorkerLogic;
use Illuminate\Console\Command;
use Throwable;

class FetchAndProcessArticlesByWorkerCommand extends Command {
    protected $signature = 'fetch-and-process-articles-by-worker {workerId}';

    protected $description = 'Fetches content from configured endpoints according to worker id, extracts article data, and prepares it for processing by the Parser Adder service';

    public function __construct(
        private FetchAndProcessEndpointForWorkerLogic $fetchAndProcessEndpointForWorkerLogic,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        $workerId = $this->argument('workerId');

        try {
            $this->fetchAndProcessEndpointForWorkerLogic->execute($workerId);
        } catch (Throwable $e) {
            throw new FetcherCommandException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, $e->getMessage() . ' Worker ID: ' . $workerId);
        }
    }
}
