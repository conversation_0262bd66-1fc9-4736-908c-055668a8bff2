<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Newswav\LaravelCentralSchema\Models\Prediction as BasePrediction;

class Prediction extends BasePrediction {
    use HasFactory;

    public function article(): BelongsTo {
        return $this->belongsTo(Article::class, 'unique_id', 'uniqueID');
    }

    public function publisher(): BelongsTo {
        return $this->belongsTo(Publisher::class, 'publisher_id');
    }
}
