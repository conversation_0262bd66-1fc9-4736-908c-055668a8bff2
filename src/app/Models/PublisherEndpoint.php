<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Newswav\LaravelCentralSchema\Models\PublisherEndpoint as BasePublisherEndpoint;

class PublisherEndpoint extends BasePublisherEndpoint {
    use HasFactory;

    public function crawlerSetting(): BelongsTo {
        return $this->belongsTo(PublisherCrawlerSetting::class, 'crawler_setting_id');
    }
}
