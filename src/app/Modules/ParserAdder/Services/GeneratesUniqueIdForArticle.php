<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Repositories\ArticleRepository;
use Carbon\Carbon;
use Illuminate\Support\Str;

class GeneratesUniqueIdForArticle {
    public function __construct(
        private ArticleRepository $articleRepository,
    ) {
    }

    public function execute(string $canonicalUrl, int $channelId): string {
        $existingArticle = $this->articleRepository->findByUrlAndChannelId($canonicalUrl, $channelId);
        if ($existingArticle !== null) {
            return $existingArticle->uniqueID;
        }

        do {
            $month    = Carbon::now()->format('m');
            $year     = Carbon::now()->format('y');
            $prefix   = "A{$year}{$month}_";
            $uniqueId = $prefix . Str::random(6);
        } while ($this->articleRepository->findWhere(['uniqueID' => $uniqueId]) !== null);

        return $uniqueId;
    }
}
