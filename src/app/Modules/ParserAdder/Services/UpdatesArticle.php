<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleObject;
use App\Repositories\ArticleRepository;

class UpdatesArticle {
    public function __construct(
        private ArticleRepository $articleRepository,
    ) {
    }

    public function execute(ArticleObject $articleObject): void {
        $this->articleRepository->updateArticleFromObject($articleObject);
    }
}
