<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Models\Article;
use App\Repositories\PredictionsRepository;
use Carbon\Carbon;

class CreatesPrediction {
    public function __construct(
        private PredictionsRepository $predictionsRepository,
    ) {
    }

    public function execute(Article $article, array $mediaWithThumbnails = []): ArticleDataWithPredictionDataObject {
        $existingPrediction = $this->predictionsRepository->getArticleDataWithPredictionData($article->uniqueID);

        if ($existingPrediction !== null) {
            return new ArticleDataWithPredictionDataObject($existingPrediction);
        }

        $createdPrediction = $this->predictionsRepository->create([
            'id'                => $article->id,
            'unique_id'         => $article->uniqueID,
            'language'          => $article->channel->language,
            'title'             => $article->title,
            'article_id'        => $article->id,
            'channel_id'        => $article->channelID,
            'published_date'    => $article->publishedDate,
            'article_url'       => $article->url,
            'media'             => $article->media,
            'category_id'       => $article->categoryID,
            'categories'        => $article->categoryIDArray,
            'tags'              => $article->tagsName,
            'channel_name'      => $article->channel->name,
            'category_name'     => '',
            'description'       => $article->description,
            'publisher_id'      => $article->channel->publisher_id,
            'topic'             => '-1',
            'prediction'        => '|-1:1|',
            'prediction_date'   => Carbon::now()->timestamp,
            'source'            => 'static',
        ]);

        if (empty($mediaWithThumbnails) === false) {
            $firstMediaWithThumbnail = null;
            foreach ($mediaWithThumbnails as $mediaWithThumbnail) {
                if ($mediaWithThumbnail->getThumbnail() !== null) {
                    $firstMediaWithThumbnail = $mediaWithThumbnail;

                    break;
                }
            }

            if ($firstMediaWithThumbnail !== null) {
                $createdPrediction->thumbnailURL    = $firstMediaWithThumbnail->getThumbnailURL();
                $createdPrediction->wideImageWidth  = $firstMediaWithThumbnail->getWideImageWidth();
                $createdPrediction->wideImageHeight = $firstMediaWithThumbnail->getWideImageHeight();
            }
        }

        // Manually set the article relationship because the article may not be loaded into slave db yet.
        // This will prevent db query to be fired and use the passed in article model directly.
        $createdPrediction->setRelation('article', $article);

        return new ArticleDataWithPredictionDataObject($createdPrediction);
    }
}
