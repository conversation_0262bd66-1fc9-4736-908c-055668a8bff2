<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use Carbon\Carbon;
use SimpleXMLElement;

class ParsesArticleWithDefaultParser {
    public function execute(string $articleData): array {
        $xml = simplexml_load_string($articleData, 'SimpleXMLElement', LIBXML_NOERROR | LIBXML_NOWARNING);

        return [
            'article_id'              => $this->getArticleId($xml),
            'title'                   => $this->getTitle($xml),
            'description'             => $this->getDescription($xml),
            'full_content'            => $this->getFullContent($xml),
            'link'                    => $this->getLink($xml),
            'cover_image_url'         => [
                'url'     => $this->getCoverImageUrl($xml),
                'caption' => null,
            ],
            'media'          => [],
            'author'         => $this->getAuthor($xml),
            'published_date' => $this->getPublishedDate($xml),
            'modified_date'  => $this->getModifiedDate($xml),
        ];
    }

    private function getArticleId(SimpleXMLElement $xml): int {

        $guid = (string) $xml->guid;
        if (preg_match('/[?&]p=(\d+)/', $guid, $matches)) {
            return (int) $matches[1];
        }

        return 0;
    }

    private function getTitle(SimpleXMLElement $xml): string {
        return (string) $xml->title;
    }

    private function getDescription(SimpleXMLElement $xml): string {
        return (string) $xml->description;
    }

    private function getFullContent(SimpleXMLElement $xml): string {
        $content = (string) ($xml->{'content:encoded'} ?? '');
        if (trim($content) !== '') {
            return $content;
        }

        $description = (string) ($xml->description ?? '');

        return trim($description);
    }

    private function getAuthor(SimpleXMLElement $xml): string {
        return (string) $xml->author;
    }

    private function getPublishedDate(SimpleXMLElement $xml): string {
        return Carbon::parse($xml->pubDate)->toDateTimeString();
    }

    private function getModifiedDate(SimpleXMLElement $xml): string {
        return Carbon::parse($xml->pubDate)->toDateTimeString();
    }

    private function getLink(SimpleXMLElement $xml): string {
        return (string) $xml->link;
    }

    private function getCoverImageUrl(SimpleXMLElement $xml): string {

        if (isset($xml->enclosure)) {
            return (string) $xml->enclosure->attributes()->url;
        }

        if (isset($xml->{'media:content'})) {
            $url = (string) $xml->{'media:content'}->attributes()->url;
            if (empty($url) === false) {
                return $url;
            }
        }

        return '';
    }
}
