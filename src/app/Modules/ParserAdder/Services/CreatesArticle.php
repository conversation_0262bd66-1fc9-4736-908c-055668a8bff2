<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\ValueObjects\ArticleObject;
use App\Models\Article;
use App\Repositories\ArticleRepository;

class CreatesArticle {
    public function __construct(
        private ArticleRepository $articleRepository,
    ) {
    }

    public function execute(ArticleObject $articleObject): Article {
        return $this->articleRepository->createArticleFromObject($articleObject);
    }
}
