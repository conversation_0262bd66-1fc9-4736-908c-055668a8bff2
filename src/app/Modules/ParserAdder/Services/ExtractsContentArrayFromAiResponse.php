<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\InvalidAiResponseException;

class ExtractsContentArrayFromAiResponse {
    /**
     * @throws InvalidAiResponseException
     */
    public function execute(string $aiResponse): array {
        $replacedInput = preg_replace('/^```(?:json)?\s*|\s*```$/i', '', trim($aiResponse));
        $replacedInput = preg_replace('/^\xEF\xBB\xBF/', '', $replacedInput);
        $replacedInput = preg_replace('/[\x00-\x1F\x7F]/', '', $replacedInput);

        if (json_validate($replacedInput) === false) {
            $jsonErrorMessage = json_last_error_msg();

            throw new InvalidAiResponseException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, $jsonErrorMessage, $aiResponse, $replacedInput);
        }

        return json_decode($replacedInput, true);
    }
}
