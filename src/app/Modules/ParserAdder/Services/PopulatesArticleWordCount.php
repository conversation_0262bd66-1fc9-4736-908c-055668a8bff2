<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Repositories\ArticleWordCountRepository;

class PopulatesArticleWordCount {
    public function __construct(
        private ArticleWordCountRepository $articleWordCountRepository,
    ) {
    }

    public function execute(string $uniqueId, string $articleData): void {
        $wordCount = str_word_count($articleData);
        if ($this->articleWordCountRepository->findWhere(['unique_id' => $uniqueId]) !== null) {
            $this->articleWordCountRepository->update('unique_id', $uniqueId, [
                'word_count' => $wordCount,
            ]);

            return;
        }
        $this->articleWordCountRepository->create([
            'unique_id'  => $uniqueId,
            'word_count' => $wordCount,
        ]);
    }
}
