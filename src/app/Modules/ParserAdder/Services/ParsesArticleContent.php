<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\AiPrompts;
use App\Classes\Constants\Parser;
use App\Exceptions\AiClientException;
use App\Exceptions\InvalidAiResponseException;
use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Modules\AiModels\AiModelClient;
use Illuminate\Support\Facades\Log;

class ParsesArticleContent {
    public function __construct(
        private ExtractsContentArrayFromAiResponse $extractsContentArrayFromAiResponse,
        private ParsesArticleWithDefaultParser $parsesArticleWithDefaultParser,
        private ValidatesParsedContent $validatesParsedContent,
        private SanitizesParsedContent $sanitizesParsedContent,
        private AiModelClient $aiModelClient,
        private ContentHelper $contentHelper,
    ) {
    }

    /**
     * @throws AiClientException
     * @throws InvalidAiResponseException
     * @throws ServiceException
     */
    public function execute(string $rawContent, string $customPrompt, bool $useAiParsing, bool $isFullHtmlContent, int $publisherId, int $channelId): array {
        $parsedContent = $this->getParsedContent($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent);

        if (empty($parsedContent) === true) {
            return [];
        }

        $validatedContent                      = $this->validatesParsedContent->execute($parsedContent, $publisherId);
        $sanitizedContent                      = $this->sanitizesParsedContent->execute($validatedContent, $publisherId, $channelId);
        $sanitizedContent[Parser::CONTENT_MD5] = md5($rawContent);

        if (isset($sanitizedContent[Parser::FULL_CONTENT]) && mb_strlen($sanitizedContent[Parser::FULL_CONTENT]) > Parser::FULL_CONTENT_MAX_LENGTH) {
            Log::info('Skipping article processing due to content length exceeding limit after sanitization. Content length: ' . mb_strlen($sanitizedContent[Parser::FULL_CONTENT]) . ', Max limit: ' . Parser::FULL_CONTENT_MAX_LENGTH);

            return [];
        }

        return $sanitizedContent;
    }

    /**
     * @throws AiClientException
     * @throws InvalidAiResponseException
     */
    private function getParsedContent(string $rawContent, string $customPrompt, bool $useAiParsing, bool $isFullHtmlContent): array {
        // Use Default Parser
        if ($useAiParsing === false) {
            $parsedContent = $this->parsesArticleWithDefaultParser->execute($rawContent);
            if ($this->validatesRequiredFields($parsedContent) === false) {
                return $this->parseWithoutArticleContent($rawContent, $customPrompt);
            }

            return $parsedContent;
        }

        // Use AI Parsing
        return $isFullHtmlContent === true
            ? $this->parseWithAi($rawContent, $customPrompt, true)
            : $this->parseWithoutArticleContent($rawContent, $customPrompt);
    }

    private function parseWithoutArticleContent(string $rawContent, string $customPrompt): array {
        $content = $this->contentHelper->getContentFromRawRssItem($rawContent);
        $parsed  = $this->parseWithAi($content['filtered_content'], $customPrompt, false);
        if (empty($parsed) === false) {
            $parsed['full_content'] = $content['full_content'];
        }

        return $parsed;
    }

    /**
     * @throws AiClientException
     * @throws InvalidAiResponseException
     */
    private function parseWithAi(string $rawArticleData, string $customPrompt, bool $isFullHtmlContent): array {
        $prompt = $customPrompt !== ''
            ? $customPrompt
            : ($isFullHtmlContent
                ? AiPrompts::EXTRACT_ARTICLE_METADATA_FROM_HTML_CONTENT_USER_PROMPT
                : AiPrompts::EXTRACT_ARTICLE_METADATA_ROM_RSS_ITEM_USER_PROMPT);

        $userPrompt = str_replace(':raw_content', $rawArticleData, $prompt)
            . ($isFullHtmlContent
                ? AiPrompts::EXTRACT_ARTICLE_METADATA_FROM_HTML_CONTENT_JSON_SCHEMA
                : AiPrompts::EXTRACT_ARTICLE_METADATA_ROM_RSS_ITEM_JSON_SCHEMA);

        return $this->extractsContentArrayFromAiResponse->execute(
            $this->aiModelClient->ask(
                AiPrompts::EXTRACT_ARTICLE_METADATA_SYSTEM_PROMPT,
                $userPrompt
            )
        );
    }

    private function validatesRequiredFields(array $parsedContent): bool {
        foreach (Parser::REQUIRED_FIELDS as $field) {
            if (empty($parsedContent[$field] ?? null)) {
                return false;
            }
        }

        return true;
    }
}
