<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use Illuminate\Support\Str;

class RetrievesImageProxyLinks {
    public function execute(bool $isThumbnail, string $url): array {
        $links = [];
        if ($isThumbnail) {
            $links['square'] = $this->generateUrlWithSecret($url, config('image_proxy.square_size'), config('image_proxy.square_size'));
            $links['wide']   = $this->generateUrlWithSecret($url, config('image_proxy.wide_width'), config('image_proxy.wide_height'));
        }

        $links['regular'] = $this->generateUrlWithoutSecret($url);

        return $links;
    }

    private function generateUrlWithSecret(string $url, int $width, int $height): string {
        $secretKey = config('image_proxy.secret_key');
        $host      = config('image_proxy.host');

        $quality = config('image_proxy.image_quality');

        $options = "{$width}x{$height},q{$quality}";

        $toSignUrl = str_replace(' ', '%20', $url); // %20 to replace the space.

        return $host . '/' . $options . ',s' . strtr(base64_encode(hash_hmac('sha256', $url, $secretKey, true)), '/+', '_-') . '/' . $toSignUrl;
    }

    private function generateUrlWithoutSecret(string $url): string {
        $host = config('image_proxy.host');
        if (Str::startsWith($url, $host)) {
            return $url;
        }

        $width   = config('image_proxy.regular_width');
        $quality = 'q' . config('image_proxy.regular_quality');

        return "{$host}/{$width}x0,{$quality}=/{$url}";
    }
}
