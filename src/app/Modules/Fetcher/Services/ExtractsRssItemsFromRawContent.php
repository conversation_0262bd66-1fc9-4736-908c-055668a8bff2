<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Repositories\ArticleRepository;
use DOMDocument;
use DOMElement;
use DOMNodeList;
use RuntimeException;

class ExtractsRssItemsFromRawContent {
    public function __construct(
        private ArticleRepository $articleRepository,
        private ContentHelper $contentHelper,
    ) {
    }

    /**
     * @throws RuntimeException
     */
    public function execute(string $rawContent): array {
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        if ($dom->loadXML($rawContent) === false) {
            throw new ServiceException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Failed to load XML');
        }

        $items    = [];
        $elements = $this->getItemElements($dom);

        foreach ($elements as $element) {
            $link = $this->contentHelper->getCanonicalUrl($this->extractLinkFromItem($element));
            $item = $dom->saveXML($element);
            if ($link !== '' && $this->articleRepository->findByUrlAndContentMd5($link, md5($item)) === null) {
                $items[] = $item;
            }
        }

        return $items;
    }

    private function extractLinkFromItem(DOMElement $item): string {
        $link = $item->getElementsByTagName('link')->item(0);
        if ($link === null) {
            $origLink = $item->getElementsByTagNameNS('*', 'origLink')->item(0);
            if ($origLink !== null) {
                return $origLink->nodeValue;
            }
            $guid = $item->getElementsByTagName('guid')->item(0);

            return $guid !== null ? $guid->nodeValue : '';

        }
        if ($link->hasAttribute('href')) {
            return $link->getAttribute('href');
        }

        return $link->nodeValue;

    }

    /**
     * @return DOMNodeList<DOMElement>
     */
    private function getItemElements(DOMDocument $dom): DOMNodeList {
        $elements = $dom->getElementsByTagName('item');

        return $elements->length > 0
            ? $elements
            : $dom->getElementsByTagName('entry');
    }
}
