<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Services;

use App\Classes\ValueObjects\ArticleProcessingDataObject;
use App\Jobs\ProcessArticleDataJob;
use Illuminate\Support\Facades\Log;

class PassesArticleDataToParserAdder {
    public function execute(int $publisherId, int $channelId, array $articleData, bool $isRss, ?string $customPrompt, bool $useBypassCloudflare, bool $useHeadlessBrowser, bool $useAiParsing): void {
        foreach ($articleData as $article) {
            Log::info('Push article to ProcessArticleDataJob queue: ' . substr($article, 0, 100) . (strlen($article) > 100 ? '...' : ''));
            ProcessArticleDataJob::dispatch(
                new ArticleProcessingDataObject(
                    $publisherId,
                    $channelId,
                    $customPrompt ?? '',
                    $useBypassCloudflare,
                    $useHeadlessBrowser,
                    $isRss === true ? $article : null,
                    $isRss === false ? $article : null,
                    $useAiParsing,
                )
            );
        }
    }
}
