<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Illuminate\Support\Facades\Log;

class FetchAndProcessEndpointForLocalFetcherLogic {
    public function __construct(
        private RetrievesArticleDataFromRawContent $retrievesArticleDataFromRawContentService,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private GuzzleReadService $guzzleReadService,
        private PassesArticleDataToParserAdder $passesArticleDataToParserAdderService,
        private ContentHelper $contentHelper,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
    }

    public function execute(string $publisherEndpointId): void {
        $endpointsWithCrawlerSettings     = $this->publisherEndpointsRepository
            ->getOneCrawlerSettingWithEndpointByEndpointId($publisherEndpointId);

        Log::info('Fetching articles for endpoint ' . $endpointsWithCrawlerSettings->endpoint);
        if ($endpointsWithCrawlerSettings->crawlerSetting->use_headless_browser === true) {
            $rawHtmlOrRss = $this->headlessBrowserService->getRawHtmlOrRss(
                $endpointsWithCrawlerSettings->endpoint,
                $endpointsWithCrawlerSettings->crawlerSetting->custom_user_agent
            );
        } elseif ($endpointsWithCrawlerSettings->crawlerSetting->to_puppeteer === true) {
            $rawHtmlOrRss = $this->bypassCloudflareService->getRawHtmlOrRss(
                $endpointsWithCrawlerSettings->endpoint,
                $endpointsWithCrawlerSettings->crawlerSetting->custom_user_agent,
                $endpointsWithCrawlerSettings->crawlerSetting->custom_cookies,
            );
        } else {
            $rawHtmlOrRss = $this->guzzleReadService->getRawHtmlOrRss(
                $endpointsWithCrawlerSettings->endpoint,
                $endpointsWithCrawlerSettings->crawlerSetting->custom_user_agent,
            );
        }

        if ($rawHtmlOrRss === null) {
            Log::warning('Skipping endpoint due to timeout or error: ' . $endpointsWithCrawlerSettings->endpoint);
            $this->publisherEndpointsRepository->updateLastChecked((int) $publisherEndpointId);

            return;
        }

        $articleDataArray = $this->retrievesArticleDataFromRawContentService->execute($rawHtmlOrRss);

        if (empty($articleDataArray) === true) {
            Log::info('No article data found for endpoint ' . $endpointsWithCrawlerSettings->endpoint);
            $this->publisherEndpointsRepository->updateLastChecked((int) $publisherEndpointId);

            return;
        }

        $this->passesArticleDataToParserAdderService->execute(
            $endpointsWithCrawlerSettings->crawlerSetting->publisher_id,
            $endpointsWithCrawlerSettings->crawlerSetting->channel_id,
            $articleDataArray,
            $this->contentHelper->isContentRss($rawHtmlOrRss->getRawContent()),
            $endpointsWithCrawlerSettings->crawlerSetting->custom_prompt,
            $endpointsWithCrawlerSettings->crawlerSetting->to_puppeteer,
            $endpointsWithCrawlerSettings->crawlerSetting->use_headless_browser,
            $endpointsWithCrawlerSettings->use_ai_parsing
        );
        Log::info('Successfully processed endpoint ' . $endpointsWithCrawlerSettings->endpoint);
    }
}
