<?php

declare(strict_types=1);

namespace App\Modules\AiModels\Services;

use App\Classes\Interfaces\AiModel;
use Illuminate\Support\Facades\Log;

class LogsAiModelEstimatedCost {
    public function execute(AiModel $model, int $inputTokens, int $outputTokens): void {
        Log::info('AI Model Cost : ' . json_encode([
            'model'         => $model->getModelName(),
            'input_tokens'  => $inputTokens,
            'output_tokens' => $outputTokens,
            'input_cost'    => 'RM' . $this->calculateThinkingCost($model->getModelName(), $inputTokens, config('ai_models_costs')),
            'output_cost'   => 'RM' . $this->calculateFinalResultCost($model, $outputTokens, config('ai_models_costs')),
            'total_cost'    => 'RM' . $this->estimateTotalCost($model, $inputTokens, $outputTokens, config('ai_models_costs')),
        ]));
    }

    public function calculateThinkingCost(string $model, int $inputTokens, array $modelCosts): float {
        $inputCostPer1M = $modelCosts[$model]['input'] ?? 0;

        return $this->formatCost($inputTokens, $inputCostPer1M);
    }

    public function calculateFinalResultCost(AiModel $model, int $outputTokens, array $modelCosts): float {
        $outputCostPer1M = $modelCosts[$model->getModelName()]['output'] ?? 0;

        return $this->formatCost($outputTokens, $outputCostPer1M);
    }

    public function estimateTotalCost(AiModel $model, int $inputTokens, int $outputTokens, array $modelCosts): float {
        $inputCostPer1M  = $modelCosts[$model->getModelName()]['input'] ?? 0;
        $outputCostPer1M = $modelCosts[$model->getModelName()]['output'] ?? 0;
        $inputCost       = $this->formatCost($inputTokens, $inputCostPer1M);
        $outputCost      = $this->formatCost($outputTokens, $outputCostPer1M);

        return round($inputCost + $outputCost, 6);
    }

    protected function formatCost(int $tokens, float $costPer1M): float {
        return round(($tokens / 1_000_000) * $costPer1M, 6) * 4;
    }
}
