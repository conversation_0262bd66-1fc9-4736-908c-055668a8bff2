APP_NAME=newswav-smart-crawler
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=newswav_news
DB_USERNAME=root
DB_PASSWORD=

CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

BYPASSCF_SERVICE_URL=
HEADLESS_BROWSER_SERVICE_URL=
BYPASSCF_TIMEOUT=30
BYPASSCF_RETRY_ATTEMPTS=3

GOOGLE_CLOUD_PROJECT_ID=
GCP_SINGLE_KEY=
GEMINI_LOCATION=

OPENAI_API_KEY=

PARSER_ADDER_SERVICE_BASE_URL=

OUTBOX_BASE_URL=
OUTBOX_API_KEY=
OUTBOX_HEADER_KEY=

BUGSNAG_API_KEY=
