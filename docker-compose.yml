services:
  php-nginx:
    build:
      context: .
      dockerfile: Dockerfile
      # args:
        # _COMPOSER_GITHUB_TOKEN: ${_COMPOSER_GITHUB_TOKEN}
    ports:
      - "8080:80"
    volumes:
      - ./src:/var/www/html
      - ./configurations/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./configurations/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./secrets:/secrets:ro
    environment:
      APP_ENV: local
    depends_on:
      - php-fpm

  php-fpm:
    image: php:8.3-fpm
    volumes:
      - ./src:/var/www/html
