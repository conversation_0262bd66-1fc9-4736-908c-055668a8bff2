# Newswav Smart Crawler

The Newswav Smart Crawler is a complete rewrite of the legacy newswav-crawlers-fetcher repo. This revamped system introduces a modular, scalable, and cloud-native architecture for crawling news content. It leverages AI models like Gemini and OpenAI for intelligent parsing, summarization, content filtering, and metadata enhancement.

## Service Components


### Fetcher Service
- Fetches URLs from the database
- Retrieves page content through a Cloudflare bypass service
- Processes content to extract article data
- Forwards results to the Parser Adder Service
- **Commands:**
    ```
    php artisan fetch-and-process-articles-by-worker <worker_id>

    OR

    php artisan crawler:fetch-process-articles-by-endpoint <publisher_endpoint_id>
    ```

### Parser Adder Service
- Parses, validates, and sanitizes article content
- Uses AI models (Gemini/OpenAI) for intelligent content extraction
- Enriches articles with metadata and formatting
- Stores processed articles in the database
- **To start server:**
    ```
    cd src && php artisan serve
    ```
- **API Endpoint**
    
    *POST* `/parse`
    
    *Body Parameters:*
    
    `publisher_id` (int, required)
    
    `channel_id` (int, required)
    
    `article_data_rss_item` (string, required; raw HTML or RSS item) or `article_data_html_link` (string, required; raw HTML or RSS item)
    
    `is_rss` (bool, required)
    
    `custom_prompt` (string, optional)


    *POST* `/parse-manually`
    
    *Body Parameters:*
    
    `publisher_id` (int, required)
    
    `channel_id` (int, required)
    
    `article_data_html_link` (string, required; raw HTML or RSS item)

### Add/Update Crawler Settings 
- This command provides an interactive CLI interface to:
    - Add new crawler setting for publishers
    - Update existing crawler configurations
    - Configure endpoints and crawling parameters
    - Set up smart crawling options
    - **Command:**
        ```
        php artisan populate-crawler-setting
        ```

## Prerequisites

Before setting up the project, ensure you have the following installed:

- **PHP 8.3+**
- **MySQL 8.0+**
- **Docker & Docker Compose** (for containerized deployment)

## Local Development Setup

### Environment Configuration
Edit `src/.env` with your configurations.


### Code Quality
```bash
composer csfixer
```