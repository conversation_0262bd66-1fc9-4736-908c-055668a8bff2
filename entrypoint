#!/bin/sh
set -e

# Check if the secret file exists
if [ -f /secrets/secret.json ]; then
  # Export each key-value pair as an environment variable
  export $(jq -r 'to_entries | map("\(.key)=\(.value|tostring)") | .[]' /secrets/secret.json)
fi

# Start PHP-FPM in background
php-fpm -R &

echo "Waiting for PHP-FPM and Laravel application to be ready..."

max_retries=5
retry_count=0

until nc -zv 127.0.0.1 9000; do
  retry_count=$((retry_count+1))

  if [ $retry_count -ge $max_retries ]; then
    echo "PHP-FPM is not ready after $retry_count attempts, exiting."
    exit 1
  fi

  echo "PHP-FPM not ready yet, retrying in 1 seconds... ($retry_count/$max_retries)"
  sleep 1
done

echo "PHP-FPM is ready and Nginx is running. Application is now ready to handle traffic."

# Start Nginx in foreground
exec nginx -g 'daemon off;'