FROM asia-southeast1-docker.pkg.dev/bustling-sunset-220007/php-83-fpm/php-8.3-fpm:latest

COPY ./configurations/php.ini /usr/local/etc/php/conf.d
WORKDIR /var/www/html

COPY src/ /var/www/html

COPY ./entrypoint /var/www

RUN rm /etc/nginx/sites-enabled/default

COPY ./configurations/nginx.conf /etc/nginx/conf.d/default.conf

RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/html/storage

RUN chmod +x /var/www/html/artisan

ARG _COMPOSER_GITHUB_TOKEN
RUN composer config --global http-basic.github.com ${_COMPOSER_GITHUB_TOKEN}
RUN COMPOSER_MEMORY_LIMIT=-1 composer install

RUN chmod +x ../entrypoint

RUN chmod -R 777 /var/www/html/storage
RUN chmod -R 777 /var/www/html/bootstrap
ENTRYPOINT ["../entrypoint"]

EXPOSE 80
