timeout: 1800s
substitutions:
  _PROJECT_APP_NAME: newswav-smart-crawler
  _PROJECT_NAME: newswav-smart-crawler

steps:
  - name: gcr.io/kaniko-project/executor
    id: Build
    secretEnv:
      - _COMPOSER_GITHUB_TOKEN
    args:
      - --destination=asia-southeast1-docker.pkg.dev/$PROJECT_ID/$_PROJECT_NAME/$_PROJECT_APP_NAME:$COMMIT_SHA
      - --cache=true
      - --build-arg=_COMPOSER_GITHUB_TOKEN

  - name: "gcr.io/cloud-builders/gcloud"
    id: Deploy
    args:
      - run
      - deploy
      - $_PROJECT_APP_NAME
      - --image=asia-southeast1-docker.pkg.dev/$PROJECT_ID/$_PROJECT_NAME/$_PROJECT_APP_NAME:$COMMIT_SHA
      - --region=asia-southeast1
      - --network=staging-vpc-asse1-nw
      - --subnet=staging-subnet-asse1-nw
      - --platform=managed
      - --allow-unauthenticated
      - --project=$PROJECT_ID
      - --port=80
      - --service-account=<EMAIL>
      - --env-vars-file=cloudbuild/development/config-variables.yaml
      - --update-secrets=/secrets/secret.json=newswav-smart-crawler-gcpsm:latest
    waitFor: ["Build"]

availableSecrets:
  secretManager:
    - versionName: projects/*************/secrets/github-read-private-package_withUsername/versions/latest
      env: "_COMPOSER_GITHUB_TOKEN"