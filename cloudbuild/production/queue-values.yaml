fullnameOverride: newswav-smart-crawler-queue-worker
nameOverride: newswav-smart-crawler-queue-worker
tier: backend
namespace: newswav

useDeploy: false

image:
  repository: asia-southeast1-docker.pkg.dev/bustling-sunset-220007/newswav-smart-crawler/newswav-smart-crawler
  tag: ""

nodeSelector:
  cloud.google.com/gke-spot: "true"

envFrom:
  - configMapRef:
      name: newswav-smart-crawler-config
  - secretRef:
      name: newswav-smart-crawler-gcpsm

service:
  enabled: false

queueWorker:
  enabled: true
  name: newswav-smart-crawler-queue-worker
  replicaCount: 0
  tier: backend
  namespace: newswav
  command: "php artisan queue:work"
  resources:
    limits:
      memory: "256Mi"
      cpu: "100m"
    requests:
      memory: "128Mi"
      cpu: "50m"
  scaledObject:
    enabled: true
  minReplicaCount: 0
  maxReplicaCount: 10
  pollingInterval: 15
  redisAddress: "**********:6379"
  queueName: "newswav_smart_crawler_database_queues:default"
  kedaListLength: "5"
  redisDatabase: "2"

serviceAccount:
  create: false
  name: newswav-smart-crawler-sa